package com.hailiang.composition

import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.bean.StudentAnswerInfo
import com.hailiang.hlutil.JsonUtil
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import com.hailiang.workcloud.data.vo.CompositionPractice
import com.hailiang.workcloud.data.vo.DoWorkStateInfo
import com.hailiang.workcloud.database.room.pojo.CompositionCheckDetailPo
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import com.hailiang.workcloud.database.room.pojo.CompositionWorkAnswerPo
import com.hailiang.workcloud.database.room.pojo.DoWorkStatePo

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/27 20:16
 */
class CompositionModel {
}

//fun CompositionBean.asWorkDetail(): WorkDetail? {
//    if (schoolworkInfo == null || schoolworkStateInfo == null) return null
//    val workDetail = WorkDetail()
//    workDetail.schoolworkId = this.schoolworkInfo.id
//    workDetail.schoolworkName = this.schoolworkInfo.name
//    workDetail.schoolworkType = this.schoolworkInfo.schoolworkType
//    workDetail.schoolworkStartTime = this.schoolworkInfo.startTime
//    workDetail.schoolworkEndTime = this.schoolworkInfo.endTime
//    workDetail.remark = this.schoolworkInfo.remark
//    workDetail.startTime = this.schoolworkStateInfo.startTime
//    workDetail.submitTime = this.schoolworkStateInfo.submitTime
//    workDetail.status = this.schoolworkStateInfo.status
//    workDetail.userId = this.schoolworkStateInfo.userId
//    workDetail.studentCheckScore = this.schoolworkStateInfo.studentCheckScore.toString()
//    workDetail.score = this.schoolworkStateInfo.score
//    workDetail.teacherCheckFlag = this.schoolworkStateInfo.teacherCheckFlag
//    workDetail.time = this.schoolworkStateInfo.time.toString()
//    return workDetail
//}

// ----------------------------------------------------------------------
fun CompositionPractice.asPo() = CompositionPracticePo(
    id = this.id,
    workId = this.workId,
    workStateId = this.workStateId,
    secondPracticeTitle = this.secondPracticeTitle,
    secondPracticeContent = this.secondPracticeContent,
)

fun CompositionPracticePo.asVo() = CompositionPractice(
    id = this.id,
    workId = this.workId,
    workStateId = this.workStateId,
    secondPracticeTitle = this.secondPracticeTitle,
    secondPracticeContent = this.secondPracticeContent,
)
fun CompositionCheckDetailPo.asVo() = CompositionCheckDetail(
    jobStatus = this.jobStatus,
    workId = this.workId,
    workStateId = this.workStateId,
    message = this.message,
    ocrTitle = this.ocrTitle,
    ocrContent = this.ocrContent,
    comprehensiveJudge = JsonUtil.parseJson(
        this.comprehensiveJudge,
        ComprehensiveJudge::class.java
    ),
    adviceList = this.adviceList?.jsonToList(Advice::class.java),
    allusionList = this.allusionList?.jsonToList(Allusion::class.java),
    score = this.score,
)

fun StudentAnswerInfo.asPo(id: Long? = null, workId: Long) = CompositionWorkAnswerPo(
    id = id, // 数据库自增id
    answerId = this.id,
    workId = workId,
    firstAnswer = this.studentFirstAnswer,
    secondAnswerDetail = this.studentSecondAnswerDetail,
    answerEditDetail = this.studentEditDetail,
    imageAnswerList = JsonUtil.objToJson(this.imgInfoList),
    imageUpdateTime = this.imageUpdateTime
)

// ----------------------------------------------------------------------
fun DoWorkStatePo.asWorkStateInfo(): DoWorkStateInfo {
    return DoWorkStateInfo(
        workId = this.workId,
        workStateId = this.workStateId,
        startTime = this.startTime,
        submitTime = this.submitTime,
        timeCounting = this.timeCounting,
        state = this.state,
        studentCheckScore = this.studentCheckScore,
        errorMessage = this.errorMessage
    )
}

fun DoWorkStateInfo.asPo(id: Long?): DoWorkStatePo {
    return DoWorkStatePo(
        id = id,
        workId = this.workId,
        workStateId = this.workStateId,
        startTime = this.startTime,
        submitTime = this.submitTime,
        timeCounting = this.timeCounting,
        state = this.state,
        studentCheckScore = this.studentCheckScore,
        errorMessage = this.errorMessage
    )
}