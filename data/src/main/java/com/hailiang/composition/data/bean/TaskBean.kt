package com.hailiang.composition.data.bean

/**
 * 任务
 */
data class TaskBean(
    val id: Long,
    /**
     * 提交类型，noLimit:不限制, write:材料书写, takePhoto:拍照, soundRecord: 录音
     */
    val submitType: String,
    val taskMaterialList: List<TaskMaterialBean>?,
)

/**
 * 任务材料
 */
data class TaskMaterialBean(
    var id: Long = 0,
    /**
     * pdf:pdf, audio:音频, picture:图片
     */
    var materialFileType: String = "",
    var materialFileUrl: String = "",
    /**
     * task:任务, answer:答案
     */
    var materialType: String = "",
    var materialFileName: String? = null,
    var materialFileMd5: String? = null,
//    var taskMaterialCorrectionList: List<TaskMaterialCorrectionBean>? = null,
)

/**
 * 任务材料批改笔迹
 */
data class TaskMaterialCorrectionBean(
    val id: Long,
    val taskId: Long,
    val taskMaterialId: Long,
    val idx: Int,
    val teacherCorrectionDetail: String?,
)