package com.hailiang.composition.data.bean

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/10 10:16
 */
@JvmInline
value class AiStreamStatus(val value: String?) {
    companion object {
        /**
         * 准备中，还不能流，例如OCR识别中等
         */
        val Preparing = AiStreamStatus("notYet")

        /**
         * 准备完毕，可以生成流
         */
        val Ready = AiStreamStatus("ready")
        val Running = AiStreamStatus("running")
        val Finished = AiStreamStatus("finished")
        val Failed = AiStreamStatus("failed")
    }
}