package com.hailiang.composition.data.source

import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.workcloud.database.room.LocalDatabase
import com.hailiang.workcloud.database.room.dao.CompositionCheckDetailDao
import com.hailiang.workcloud.database.room.dao.CompositionPracticeDao
import com.hailiang.workcloud.database.room.dao.CompositionWorkAnswerDao
import com.hailiang.workcloud.database.room.pojo.CompositionCheckDetailPo
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import com.hailiang.workcloud.database.room.pojo.CompositionWorkAnswerPo
import kotlinx.coroutines.flow.Flow

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/25 20:17
 */
class CompositionLocalDataSource {
    private val compositionLocalPracticeDao: CompositionPracticeDao by lazy {
        LocalDatabase.database.getCompositionLocalPracticeDao()
    }
    private val compositionCheckDetailDao: CompositionCheckDetailDao by lazy {
        LocalDatabase.database.getCompositionCheckDetailDao()
    }
    private val compositionWorkAnswerDao: CompositionWorkAnswerDao by lazy {
        LocalDatabase.database.getCompositionWorkAnswerDao()
    }

    fun queryCompositionPractice(workId: Long, workStateId: Long): CompositionPracticePo? {
        return compositionLocalPracticeDao.getByWorkIdAndStateId(
            workId = workId,
            workStateId = workStateId
        )
    }

    fun updateCompositionPractice(practice: CompositionPracticePo) {
        HLog.i(HTag.TAG, "更新本地议论文信息 updateCompositionPractice: $practice")
        compositionLocalPracticeDao.insertOrUpdate(practice)
    }

    fun updateCompositionPractice(
        workId: Long,
        workStateId: Long,
        title: String?,
        content: String?,
    ) {
        // 仅更新，不做插入。没有数据表示接口识别或者，获取清理过数据了
        compositionLocalPracticeDao.updateByWorkStateId(
            workId = workId,
            workStateId = workStateId,
            title = title,
            content = content
        )
    }

    fun deleteCompositionPractice(workId: Long, workStateId: Long) {
        HLog.i(HTag.TAG, "清空本地议论文做题信息: workId: $workId; workStateId: $workStateId")
        compositionLocalPracticeDao.deleteByWorkIdAndStateId(
            workId = workId,
            workStateId = workStateId
        )
    }

    // ----------------------------------------------------------------------
    /**
     * 获取AI批改信息
     */
    fun queryCompositionAiCheckDetail(workId: Long, workStateId: Long): CompositionCheckDetailPo? {
        return compositionCheckDetailDao.getAiCheckDetail(
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 获取教师批改信息
     */
    fun queryCompositionTeacherCheckDetail(workId: Long, workStateId: Long): CompositionCheckDetailPo? {
        return compositionCheckDetailDao.getTeacherCheckDetail(
            workId = workId,
            workStateId = workStateId,
        )
    }

    fun updateCompositionCheckDetail(checkDetail: CompositionCheckDetailPo) {
        HLog.i(HTag.TAG, "更新议论文批改信息 updateCompositionCheckDetail: $checkDetail")
        compositionCheckDetailDao.insertOrUpdate(checkDetail)
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    fun deleteCompositionTempImages(workId: Long) {
//        DBUtils.deleteWhere(
//            PictureAnswerInfo::class.java,
//            PictureAnswerInfoDao.Properties.WorkId.eq(workId)
//        )
    }
    // ----------------------------------------------------------------------
    fun queryCompositionWorkAnswer(workId: Long): CompositionWorkAnswerPo? {
        return compositionWorkAnswerDao.queryByWorkId(workId)
    }

    fun queryCompositionWorkAnswerWithFlow(workId: Long): Flow<CompositionWorkAnswerPo?> {
        return compositionWorkAnswerDao.queryByWorkIdWithFlow(workId)
    }

    fun updateCompositionWorkAnswer(compositionWorkAnswerPo: CompositionWorkAnswerPo?) {
        compositionWorkAnswerPo ?: return
        compositionWorkAnswerDao.insertOrUpdate(compositionWorkAnswerPo)
    }
    // ----------------------------------------------------------------------
}