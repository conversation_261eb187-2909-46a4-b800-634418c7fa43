package com.hailiang.composition.data.bean

import com.hailiang.composition.data.AiStreamClient

data class AiStreamTopicAnalysisResponse(
    /**
     * 推理思考内容
     */
    val reasoning: String?,
    /**
     * 正式回复内容
     */
    val content: String?,
    val finishReason: String?,
    val topicAnalysis: String?,
    val conceptAnalysis:String?
): AiStreamClient.StreamChunk {

    override fun reasonContent(): String? {
        return reasoning
    }

    override fun answerContent(): String? {
        return content
    }

    override fun isCompletionMessage(): Boolean {
        return finishReason == AiStreamReason.Stop.value
    }

    override fun isError(): Boolean {
        return finishReason == AiStreamReason.Error.value
    }

    override fun finishReason(): String? {
        return finishReason
    }
}
