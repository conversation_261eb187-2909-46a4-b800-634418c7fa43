package com.hailiang.composition.data.bean

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/10 10:16
 */
@JvmInline
value class AiStreamReason(val value: String?) {
    companion object {
        val Default = AiStreamReason("")
        val Stop = AiStreamReason("stop")
        val Error = AiStreamReason("error")
        val Failed = AiStreamReason(AiStreamStatus.Companion.Failed.value)
        val NetworkError = AiStreamReason("NetworkError")
    }

    fun isNetworkError(): Boolean {
        return value == NetworkError.value
    }

    fun isStreamError(): Boolean {
        return value == Error.value || value == Failed.value
    }
}