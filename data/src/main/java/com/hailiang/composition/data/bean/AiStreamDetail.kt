package com.hailiang.composition.data.bean

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/7 14:43
 */
data class AiStreamDetail(
    val reasoning: String?,
    val answering: String?,
    val finishReason: String?,
) {
    companion object {
        fun obtainErrorDetail(
            reasoning: String? = null,
            answering: String? = null,
        ): AiStreamDetail {
            return AiStreamDetail(
                reasoning = reasoning,
                answering = answering,
                finishReason = AiStreamReason.Error.value
            )
        }
    }

    fun isStreamError(): Boolean {
        return finishReason == AiStreamReason.Error.value
    }

    fun isCompletion(): Boolean {
        return finishReason == AiStreamReason.Stop.value
    }

    fun isSuccess(): Boolean {
        return finishReason.isNullOrEmpty() || isCompletion()
    }

    fun isNetworkError(): Bo<PERSON>an {
        return finishReason == AiStreamReason.NetworkError.value
    }

    fun isEmpty(): <PERSON><PERSON><PERSON> {
        return reasoning.isNullOrEmpty() && answering.isNullOrEmpty()
    }

    fun getAiStreamReason(): AiStreamReason {
        return AiStreamReason(finishReason)
    }
}