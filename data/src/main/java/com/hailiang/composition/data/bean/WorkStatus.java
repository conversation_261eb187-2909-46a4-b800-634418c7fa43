package com.hailiang.composition.data.bean;

/**
 * @Description: 服务端注解： 0未提交 1 答题中 2 已提交 3已自批 4已查看讲评 5推送题  8做推送 9 推送已提交 10 订正 11 普通题目提交
 * 本地之前到注解：0未做 1.继续做 2：已做完题 3：继续自批 4：已提交 5.已自批 6.查看错题(提交前) 7.已批改（老师已批改）8: 推送题做题 11：推送题继续做题  12.继续订正
 * <AUTHOR>
 * @Date 2024/4/3 20:53
 */
public interface WorkStatus {
    /**
     * 0 未做
     */
    int UN_DO = 0;

    /**
     * 1 答题中
     */
    int DOING = 1;

    /**
     * 2 已做完题
     * 议论文保存到了云端。
     * 其他类型保存到了本地。
     */
    int DONE = 2;

    /**
     * 3 自批、二次作答
     */
    int SELF_CORRECT = 3;


    /**
     * 4 已全部提交(已查看讲评)
     */
    int ALL_SUBMITTED = 4;
}
