package com.hailiang.composition.data.repository

import com.hailiang.composition.asPo
import com.hailiang.composition.asWorkStateInfo
import com.hailiang.composition.data.source.WorkLocalDataSource
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.workcloud.data.vo.DoWorkStateInfo

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/5/23 17:35
 */
class WorkReposition {
    private val workLocalDataSource by lazy { WorkLocalDataSource() }

    fun queryWorkState(workId: Long, workStateId: Long): DoWorkStateInfo? {
        if(workId <= 0 || workStateId <= 0) return null
        return workLocalDataSource.queryWorkState(workId, workStateId)?.asWorkStateInfo()
    }

    fun updateWorkState(workId: Long, workStateId: Long, newState: Int) {
        HLog.i(HTag.TAG, "updateWorkState workId: $workId >> $newState")
        workLocalDataSource.updateWorkState(workId, workStateId, newState)
    }

    fun updateWorkState(workState: DoWorkStateInfo) {
        HLog.i(HTag.TAG, "updateWorkState: workState; $workState")
        if (workState.workId <= 0 || workState.workStateId <= 0) return
        workLocalDataSource.updateWorkState(
            workState.asPo(
                id = workLocalDataSource.queryWorkState(
                    workId = workState.workId,
                    workStateId = workState.workStateId
                )?.id
            )
        )
    }

    fun clearWorkErrorMessage(workId: Long, workStateId: Long) {
        workLocalDataSource.clearWorkErrorMessage(workId = workId, workStateId = workStateId)
    }

}