package com.hailiang.composition.data.source

import com.alibaba.fastjson2.JSONObject
import com.hailiang.composition.data.AiStreamClient
import com.hailiang.composition.data.ApiService
import com.hailiang.composition.data.CompositionListWrapper
import com.hailiang.composition.data.bean.AiGuidance
import com.hailiang.composition.data.bean.AiStreamChunkResponse
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.CompositionCheckBean
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.CreateWorkBean
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.request.CompositionSecondSubmitRequest
import com.hailiang.composition.data.bean.request.FeedbackEvaluateRequest
import com.hailiang.composition.data.bean.response.FirstSubmitResponse
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.libhttp.BaseHttpResult
import com.hailiang.libhttp.BaseRetrofit
import com.hailiang.libhttp.build.RequestBodyBuilder
import com.hailiang.libhttp.request.HlRequest
import com.hailiang.libhttp.request.HlRequest.Method
import com.hailiang.libhttp.request.HlRequestBody
import com.hailiang.libhttp.utils.HttpUtil
import kotlinx.coroutines.flow.Flow

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/25 20:17
 */
class CompositionRemoteDataSource {

    private val apiService by lazy {
        BaseRetrofit.getInstance().create(ApiService::class.java)
    }

    suspend fun getCompositionList(
        page: Int,
        pageSize: Int,
        subject: Int,
    ): BaseHttpResult<CompositionListWrapper> {
        val params = mutableMapOf<String, Any>()
        params["currPage"] = page
        params["pageSize"] = pageSize
        params["subject"] = subject
        return apiService.getCompositionList(params)
    }

    /**
     * 删除作文
     */
    suspend fun deleteComposition(schoolworkStateId: Long): BaseHttpResult<Any> {
        val params = mapOf("schoolworkStateId" to schoolworkStateId)
        return apiService.deleteComposition(params)
    }

    /**
     * 创建或更新作文
     */
    suspend fun createOrUpdateComposition(createWorkBean: CreateWorkBean): BaseHttpResult<FirstSubmitResponse> {
        return apiService.addComposition(RequestBodyBuilder.createJson(createWorkBean))
    }

    /**
     * 请求作文信息
     */
    suspend fun requestCompositionInfo(workId: Long): BaseHttpResult<WorkDetail> {
        val params = mapOf("schoolworkId" to workId)
        return apiService.requestCompositionInfo(params)
    }

    /**
     * 获取学生作业AI批改详情
     */
    suspend fun requestStudentAiCorrectInfo(workStateId: Long): BaseHttpResult<CompositionCheckBean> {
        val params = mapOf("schoolworkStateId" to workStateId)
        return apiService.requestStudentAiCorrectInfo(params)
    }


//    suspend fun requestJudgeJobAdd(
//        schoolworkStateId: Long,
//        imageUrlList: List<String>,
//    ): BaseHttpResult<Any> {
//        val params = mutableMapOf<String, Any>()
//        params.put("schoolworkStateId", schoolworkStateId)
//        params.put("imageUrlList", JSONArray(imageUrlList))
//        return apiService.requestJudgeJobAdd(params)
//    }

    /**
     * 二次作答提交
     */
    suspend fun submitStudentSecondPracticeInfo(requestBean: CompositionSecondSubmitRequest): BaseHttpResult<Any> {
        return apiService.studentCompositionSecondSubmit(requestBean)
    }

    // ----------------------------------------------------------------------
    suspend fun addFeedbackEvaluate(requestBean: FeedbackEvaluateRequest.Add): BaseHttpResult<Any> {
        return apiService.addFeedbackEvaluate(requestBean)
    }

    /**
     * 取消赞
     */
    suspend fun cancelFeedbackEvaluate(requestBean: FeedbackEvaluateRequest.Delete): BaseHttpResult<Any> {
        return apiService.cancelFeedbackEvaluate(requestBean)
    }
    // ----------------------------------------------------------------------
    /**
     * 重试AI首次批改
     */
    suspend fun retryAiFirstJudge(workStateId: Long): BaseHttpResult<Any> {
        val jsonObj = JSONObject()
        jsonObj.put("schoolworkStateId", workStateId)
        return apiService.retryAiFirstJudge(jsonObj)
    }

    /**
     * 重试AI二次批改
     */
    suspend fun retryAiSecondJudge(workStateId: Long): BaseHttpResult<Any> {
        val jsonObj = JSONObject()
        jsonObj.put("schoolworkStateId", workStateId)
        return apiService.retryAiSecondJudge(jsonObj)
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    /**
     * 检查所有AI流状态
     */
    suspend fun checkAiStreamStatus(
        workId: Long,
        workStateId: Long,
    ): BaseHttpResult<CompositionStatusResponse> {
        HLog.i(HTag.TAG, "检查所有AI批改流状态: workId: ${workId}; workStateId: $workStateId")
        val jsonObj = JSONObject()
        jsonObj.put("schoolworkStateId", workStateId)
        return apiService.checkAiStreamStatus(RequestBodyBuilder.createJson(jsonObj))
    }
    // ----------------------------------------------------------------------
    // 首次作答 AI 批改流
    // ----------------------------------------------------------------------
    /**
     * 首次作答内容AI流 - 初始化，第一次用
     */
    fun requestFirstContentAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（首次作答内容-初始化）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_first_content_init",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_first_judge/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 首次作答内容AI流 - 历史
     */
    fun requestFirstContentAiStreamHistory(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（首次作答内容-历史）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_first_content_history",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_first_judge/history/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 首次作答内容AI流 - 重试
     */
    fun retryFirstContentAiStream(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（首次作答内容-重试）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_first_content_retry",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_first_judge/retry/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }
    // ----------------------------------------------------------------------
    // 审题立意
    // ----------------------------------------------------------------------
    /**
     * 审题立意AI流-初始化，第一次用
     */
    fun requestTopicAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（审题立意-初始化）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_topic_init",
            path = "/tch-schoolwork/v1/ai_model/article/student/topic_analysis/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 审题立意AI流 - 历史
     */
    fun requestTopicAiStreamHistory(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（审题立意 - 历史）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_topic_history",
            path = "/tch-schoolwork/v1/ai_model/article/student/topic_analysis/history/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 审题立意AI流 - 重试
     */
    fun retryTopicAiStream(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（审题立意-重试）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_topic_retry",
            path = "/tch-schoolwork/v1/ai_model/article/student/topic_analysis/retry/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }
    // ----------------------------------------------------------------------
    // 二次 AI 批改
    // ----------------------------------------------------------------------
    /**
     * 二次作答内容AI流-初始化，第一次用
     */
    fun requestSecondContentAiStreamInfo(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（二次作答内容-初始化）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_second_content_init",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_second_judge/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 二次作答内容AI流 - 历史
     */
    fun requestSecondContentAiStreamHistory(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（二次作答内容 - 历史）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_second_content_history",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_second_judge/history/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    /**
     * 二次作答内容AI流 - 重试
     */
    fun retrySecondContentAiStream(
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        HLog.i(
            HTag.TAG,
            "请求AI流数据（二次作答内容-重试）: workId: ${workId}; workStateId: $workStateId"
        )
        return requestAiStream(
            connectionId = "${workId}_${workStateId}_second_content_retry",
            path = "/tch-schoolwork/v1/ai_model/article/student/ai_second_judge/retry/stream",
            sseClient = sseClient,
            workId = workId,
            workStateId = workStateId,
        )
    }

    private fun requestAiStream(
        connectionId: String,
        path: String,
        sseClient: AiStreamClient<AiStreamChunkResponse>,
        workId: Long,
        workStateId: Long,
    ): Flow<AiStreamDetail?> {
        val bodyJson = JSONObject()
        bodyJson.put("schoolworkId", workId)
        bodyJson.put("schoolworkStateId", workStateId)
        val request = HlRequest(
            url = HttpUtil.buildUrl(BaseRetrofit.getBaseUrl(), path),
            method = Method.POST,
            requestBody = HlRequestBody.create(content = bodyJson)
        )
        return sseClient.startSSE(connectionId = connectionId, request = request)
    }
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    /**
     * 新手引导
     */
    suspend fun addBeginnerGuidance(): BaseHttpResult<Any> {
        val params = mapOf("type" to "takingPhoto")
        return apiService.addBeginnerGuidance(params)
    }

    suspend fun getBeginnerGuidance(): BaseHttpResult<AiGuidance> {
        val params = mapOf("type" to "takingPhoto")
        return apiService.getBeginnerGuidance(params)
    }

    suspend fun getTextCorrectionInfo(): BaseHttpResult<JSONObject> {
        return apiService.getTextCorrectionInfo()
    }
}