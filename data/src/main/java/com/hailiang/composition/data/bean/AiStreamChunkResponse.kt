package com.hailiang.composition.data.bean

import com.hailiang.composition.data.AiStreamClient


/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/7 14:43
 */
data class AiStreamChunkResponse(
    /**
     * 推理思考内容
     */
    val reasoning: String?,
    /**
     * 正式回复内容
     */
    val content: String?,
    val finishReason: String?,
) : AiStreamClient.StreamChunk {

    override fun reasonContent(): String? {
        return reasoning
    }

    override fun answerContent(): String? {
        return content
    }

    override fun isCompletionMessage(): Boolean {
        return finishReason == AiStreamReason.Companion.Stop.value
    }

    override fun isError(): Boolean {
        return finishReason == AiStreamReason.Companion.Error.value
    }

    override fun finishReason(): String? {
        return finishReason
    }
}