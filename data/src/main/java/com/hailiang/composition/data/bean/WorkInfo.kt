package com.hailiang.composition.data.bean

data class WorkInfo(
    val userId: String,
    val subject: Int,
    val schoolworkId: Long,
    val schoolworkStateId: Long,
    val imgInfoList: List<ImageAnswerBean>?,
    /**
     * 状态：init-初始化，running-进行中, success-成功, failed-失败
     * JobStatus
     */
    val commentFirstStatus: String,
    /**
     * 状态：init-初始化，running-进行中, success-成功, failed-失败
     * JobStatus
     */
    val ocrStatus: String?,
    val remark: String?,
    val score: Long?,
    /**
     * 答题状态  0未提交 1答题中 2已提交 3已自批 4已查看讲评 5推送题  8做推送 9 推送已提交 10 订正 11 普通题目提交
     */
    val status: Int,
    val studentCheckScore: String?,
    val studentFirstAnswerDetail: String?,
    val studentSecondAnswerDetail: String?,
    val teacherFirstCheckScore: String?,
    val createTime: String
)