package com.hailiang.composition.data.bean

import com.hailiang.composition.data.bean.CompositionCheckBean.OcrResult
import com.hailiang.composition.data.enums.JobStatus

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/8 16:53
 */
data class AiStreamStatusResponse(
    /**
     * notYet 还不能流，ready 可以流，running 流中，finished 流完成，failed 流失败了
     */
    val streamStatus: String?,
    val ocrResult: OcrResult?,
) {
    fun getOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(ocrResult?.status)
    }

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun preparing(): Bo<PERSON><PERSON> {
        return streamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun isReady(): Boolean {
        return streamStatus == AiStreamStatus.Ready.value
    }

    fun isRunning(): Boolean {
        return streamStatus == AiStreamStatus.Running.value
    }

    fun isFinished(): Boolean {
        return streamStatus == AiStreamStatus.Finished.value
    }

    fun isFailed(): Bo<PERSON><PERSON> {
        return streamStatus == AiStreamStatus.Failed.value
    }
}