package com.hailiang.composition.data.bean

import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag

data class CompositionStatusResponse(
    //notYet 还不能流，ready 可以流，streaming 流中，finished 流完成，failed 流失败了
    var topicStreamStatus: String? = null,
    var streamStatus: String? = null,
    var secondJudgeStreamStatus: String? = null,
    var titleOcrResult: OcrResult? = null,
    var ocrResult: OcrResult? = null,
) {

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun titlePreparing(): Boolean {
        return topicStreamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun titleIsReady(): Boolean {
        return topicStreamStatus == AiStreamStatus.Ready.value
    }

    fun titleIsRunning(): Boolean {
        return topicStreamStatus == AiStreamStatus.Running.value
    }

    fun titleIsFinished(): Boolean {
        return topicStreamStatus == AiStreamStatus.Finished.value
    }

    fun titleIsFailed(): Boolean {
        return topicStreamStatus == AiStreamStatus.Failed.value
    }

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun firstPreparing(): Boolean {
        return streamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun firstIsReady(): Boolean {
        return streamStatus == AiStreamStatus.Ready.value
    }

    fun firstIsRunning(): Boolean {
        return streamStatus == AiStreamStatus.Running.value
    }

    fun firstIsFinished(): Boolean {
        return streamStatus == AiStreamStatus.Finished.value
    }

    fun firstIsFailed(): Boolean {
        return streamStatus == AiStreamStatus.Failed.value
    }

    /**
     * 准备中，还不能流，例如OCR识别中等
     */
    fun secondPreparing(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Preparing.value
    }

    /**
     * 可以生成流
     */
    fun secondIsReady(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Ready.value
    }

    fun secondIsRunning(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Running.value
    }

    fun secondIsFinished(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Finished.value
    }

    fun secondIsFailed(): Boolean {
        return secondJudgeStreamStatus == AiStreamStatus.Failed.value
    }

    // ----------------------------------------------------------------------
    fun getOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(ocrResult?.status)
    }

    fun getTitleOcrStatus(): JobStatus {
        return JobStatus.getEnumByValue(titleOcrResult?.status)
    }

    fun isEmpty(): Boolean {
        return topicStreamStatus.isNullOrEmpty()
                && streamStatus.isNullOrEmpty()
                && secondJudgeStreamStatus.isNullOrEmpty()
                && titleOcrResult == null
                && ocrResult == null
    }

    fun prepared(): Boolean {
        return getOcrJobStatus().isSuccess() && getTitleOcrStatus().isSuccess()
    }

    // ----------------------------------------------------------------------
    fun getRunningType(): AiStreamType {
        return when {
            titleIsRunning() -> {
                HLog.d(HTag.TAG, "审题立意中")
                AiStreamType.Title
            }

            firstIsRunning() -> {
                HLog.d(HTag.TAG, "第一次批改中")
                AiStreamType.First
            }

            secondIsRunning() -> {
                HLog.d(HTag.TAG, "二次批改中")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }

    fun getPreparingType(): AiStreamType {
        return when {
            titlePreparing() -> {
                HLog.d(HTag.TAG, "审题立意准备中")
                AiStreamType.Title
            }

            firstPreparing() -> {
                HLog.d(HTag.TAG, "一流准备中")
                AiStreamType.First
            }

            secondPreparing() -> {
                HLog.d(HTag.TAG, "二流准备中")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }
    fun getReadyType(): AiStreamType {
        return when {
            titleIsReady() -> {
                HLog.d(HTag.TAG, "审题立意准备完毕")
                AiStreamType.Title
            }

            firstIsReady() -> {
                HLog.d(HTag.TAG, "一流准备完毕")
                AiStreamType.First
            }

            secondIsReady() -> {
                HLog.d(HTag.TAG, "二流准备完毕")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }
    fun getFinishType(): AiStreamType {
        return when {
            secondIsFinished() -> {
                HLog.d(HTag.TAG, "二流成功")
                AiStreamType.Second
            }
            firstIsFinished() -> {
                HLog.d(HTag.TAG, "一流成功")
                AiStreamType.First
            }
            titleIsFinished() -> {
                HLog.d(HTag.TAG, "审题立意成功")
                AiStreamType.Title
            }
            else -> {
                AiStreamType.Default
            }
        }
    }
    fun getFailedType(): AiStreamType {
        return when {
            titleIsFailed() -> {
                HLog.d(HTag.TAG, "审题立意失败")
                AiStreamType.Title
            }

            firstIsFailed() -> {
                HLog.d(HTag.TAG, "一流失败")
                AiStreamType.First
            }

            secondIsFailed() -> {
                HLog.d(HTag.TAG, "二流失败")
                AiStreamType.Second
            }

            else -> {
                AiStreamType.Default
            }
        }
    }
}

data class OcrResult(
    var message: String? = null,
    var status: String? = null,
    var createTime: String? = null,

    ) {
    fun getOcrJobStatus(): JobStatus {
        return JobStatus.getEnumByValue(status)
    }
}
