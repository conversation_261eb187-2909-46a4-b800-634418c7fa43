{"formatVersion": 1, "database": {"version": 2, "identityHash": "71604b0fe1a240d4c5bde3a42f186f46", "entities": [{"tableName": "handwrite_history", "createSql": "CREATE TABLE IF NOT EXISTS `${TABLE_NAME}` (`id` INTEGER PRIMARY KEY AUTOINCREMENT, `work_id` INTEGER NOT NULL, `question_id` INTEGER NOT NULL, `sub_question_id` INTEGER NOT NULL, `task_id` INTEGER NOT NULL, `material_id` INTEGER NOT NULL, `idx` INTEGER NOT NULL, `tag` TEXT NOT NULL, `strokes` TEXT NOT NULL, `update_time` INTEGER NOT NULL)", "fields": [{"fieldPath": "id", "columnName": "id", "affinity": "INTEGER", "notNull": false}, {"fieldPath": "workId", "columnName": "work_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "questionId", "columnName": "question_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "subQuestionId", "columnName": "sub_question_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "taskId", "columnName": "task_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "materialId", "columnName": "material_id", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "idx", "columnName": "idx", "affinity": "INTEGER", "notNull": true}, {"fieldPath": "tag", "columnName": "tag", "affinity": "TEXT", "notNull": true}, {"fieldPath": "strokes", "columnName": "strokes", "affinity": "TEXT", "notNull": true}, {"fieldPath": "updateTime", "columnName": "update_time", "affinity": "INTEGER", "notNull": true}], "primaryKey": {"autoGenerate": true, "columnNames": ["id"]}, "indices": [], "foreignKeys": []}], "views": [], "setupQueries": ["CREATE TABLE IF NOT EXISTS room_master_table (id INTEGER PRIMARY KEY,identity_hash TEXT)", "INSERT OR REPLACE INTO room_master_table (id,identity_hash) VALUES(42, '71604b0fe1a240d4c5bde3a42f186f46')"]}}