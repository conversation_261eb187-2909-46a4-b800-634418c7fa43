package com.hailiang.workcloud.database.room.dao

import androidx.room.Dao
import androidx.room.Insert
import androidx.room.OnConflictStrategy
import androidx.room.Query
import com.hailiang.workcloud.database.room.pojo.HandwriteHistoryPo

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2024/7/17 16:55
 */
@Dao
interface HandwriteHistoryDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdateHistory(list: List<HandwriteHistoryPo>)

    @Query("SELECT * FROM handwrite_history WHERE work_id=:workId AND idx = :idx")
    fun queryHandwriteHistory(workId: Long, idx: Int): List<HandwriteHistoryPo>

    @Query("SELECT * FROM handwrite_history WHERE tag = :tag")
    fun queryHandwriteHistoryByTag(tag: String): List<HandwriteHistoryPo>

    @Query("DELETE FROM handwrite_history WHERE update_time < :timeMillis")
    fun deleteBefore(timeMillis: Long)

    @Query("DELETE FROM handwrite_history WHERE work_id=:workId AND (id not in (SELECT id FROM handwrite_history where work_id = :workId ORDER BY update_time DESC LIMIT :limit))")
    fun deleteOverFlow(workId: Long, limit: Long)

    @Query("SELECT count(id) FROM handwrite_history WHERE work_id = :workId")
    fun countHistory(workId: Long): Int

}