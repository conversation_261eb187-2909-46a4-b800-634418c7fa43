package com.hailiang.workcloud.database.room.dao

import androidx.room.*
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import com.hailiang.workcloud.database.room.pojo.CompositionWorkAnswerPo
import kotlinx.coroutines.flow.Flow

/**
 * Description: 服务端返回到学生作答信息
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
@Dao
interface CompositionWorkAnswerDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdate(studentWorkAnswer: CompositionWorkAnswerPo): Long

    /**
     * 根据作业ID查询作答记录
     * @param workId 作业ID
     * @return 学生作答信息
     */
    @Query("SELECT * FROM composition_work_answer WHERE work_id = :workId")
    fun queryByWorkId(workId: Long): CompositionWorkAnswerPo?

    @Query("SELECT * FROM composition_work_answer WHERE work_id = :workId")
    fun queryByWorkIdWithFlow(workId: Long): Flow<CompositionWorkAnswerPo?>
}