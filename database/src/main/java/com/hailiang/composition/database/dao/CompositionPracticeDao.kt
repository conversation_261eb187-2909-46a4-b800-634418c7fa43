package com.hailiang.workcloud.database.room.dao

import androidx.room.*
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import kotlinx.coroutines.flow.Flow

/**
 * Description: 议论文本地作答记录，提交成功后可以删除
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
@Dao
interface CompositionPracticeDao {

    /**
     * 插入/更新作答记录
     * @param practice 作答记录
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdate(practice: CompositionPracticePo)

    /**
     * 更新作答记录
     * @param practice 作答记录
     */
    @Query("UPDATE composition_practice SET second_practice_title = :title , second_practice_content= :content WHERE work_id = :workId AND work_state_id = :workStateId")
    fun updateByWorkStateId(workId: Long, workStateId: Long, title: String?, content: String?)

    /**
     * 根据作业ID和状态ID查询作答记录
     * @param workId 作业ID
     * @param workStateId 作业状态ID
     * @return 作答记录
     */
    @Query("SELECT * FROM composition_practice WHERE work_id = :workId AND work_state_id = :workStateId")
    fun getByWorkIdAndStateId(workId: Long, workStateId: Long): CompositionPracticePo?

    @Query("DELETE FROM composition_practice WHERE work_id = :workId AND work_state_id = :workStateId")
    fun deleteByWorkIdAndStateId(workId: Long, workStateId: Long)
}