package com.hailiang.workcloud.database.room.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Description: 本地 议论文作答记录表
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
@Entity(tableName = "composition_check_detail")
data class CompositionCheckDetailPo(

    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    /**
     * 初始化 init 进行中 running 成功 success 失败 failed
     */
    @ColumnInfo(name = "job_status")
    val jobStatus: String,

    /**
     * 额外信息
     */
    val message: String?,
    /**
     * 0 表示AI，否则表示具体的批改人
     */
    @ColumnInfo(name = "check_user_id")
    val checkUserId: Long,
    /**
     * 作业id：服务端
     */
    @ColumnInfo(name = "work_id")
    val workId: Long,

    /**
     * 作业状态id：服务端
     */
    @ColumnInfo(name = "work_state_id")
    val workStateId: Long,

    /**
     * OCR识别结果
     */
    @ColumnInfo(name = "ocr_title")
    val ocrTitle: String?,
    /**
     * OCR识别结果
     */
    @ColumnInfo(name = "ocr_content")
    val ocrContent: String?,

    /**
     * 综合评价
     */
    @ColumnInfo(name = "comprehensive_judge")
    val comprehensiveJudge: String?,
    /**
     * 点拨
     */
    @ColumnInfo(name = "advice_list")
    val adviceList: String?,

    /**
     * 典故列表（知识加油站）
     */
    @ColumnInfo(name = "allusion_list")
    val allusionList: String?,

    /**
     * 分数
     */
    val score: Int,

    /**
     * 本地数据更新时间
     */
    @ColumnInfo(name = "update_time")
    val updateTime: Long,
)