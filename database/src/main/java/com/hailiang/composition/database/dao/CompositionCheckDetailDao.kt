package com.hailiang.workcloud.database.room.dao

import androidx.room.*
import com.hailiang.workcloud.database.room.pojo.CompositionCheckDetailPo
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import kotlinx.coroutines.flow.Flow

/**
 * Description: 议论文批改情况
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
@Dao
interface CompositionCheckDetailDao {

    @Insert(onConflict = OnConflictStrategy.REPLACE)
    fun insertOrUpdate(practice: CompositionCheckDetailPo)

    /**
     * 根据作业ID和状态ID查询
     * @param workId 作业ID
     * @param workStateId 作业状态ID
     * @param checkUserId 0 表示AI，其他表示具体的老师
     * @return 作答记录
     */
    @Query("SELECT * FROM composition_check_detail WHERE work_id = :workId AND work_state_id = :workStateId AND check_user_id = 0")
    fun getAiCheckDetail(workId: Long, workStateId: Long): CompositionCheckDetailPo?

    /**
     * 根据作业ID和状态ID查询
     * @param workId 作业ID
     * @param workStateId 作业状态ID
     * @param checkUserId 0 表示AI，其他表示具体的老师
     * @return 作答记录
     */
    @Query("SELECT * FROM composition_check_detail WHERE work_id = :workId AND work_state_id = :workStateId AND check_user_id > 0  Limit 1")
    fun getTeacherCheckDetail(workId: Long, workStateId: Long, ): CompositionCheckDetailPo?

//    @Query("DELETE FROM composition_check_detail WHERE work_id = :workId AND work_state_id = :workStateId")
//    fun deleteByWorkIdAndStateId(workId: Long, workStateId: Long)
}