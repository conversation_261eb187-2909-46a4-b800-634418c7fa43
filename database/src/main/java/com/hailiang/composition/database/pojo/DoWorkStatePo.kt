package com.hailiang.workcloud.database.room.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * 做题状态表，用于记录学生的本地做题状态
 * 目前支持 议论文。
 * 其他作业数据依然是在 WorkDetail 中，后续迁移
 * 后续 WorkDetail 仅用于同步远程数据
 */
@Entity(tableName = "do_work_state")
data class DoWorkStatePo(
    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    /**
     * 作业id：服务端
     */
    @ColumnInfo(name = "work_id")
    val workId: Long,

    /**
     * 作业状态id：服务端
     */
    @ColumnInfo(name = "work_state_id")
    val workStateId: Long,

    @ColumnInfo(name = "start_time")
    val startTime: Long,

    @ColumnInfo(name = "submit_time")
    val submitTime: Long,

    /**
     * 计时，做题耗时
     */
    @ColumnInfo(name = "time_counting")
    val timeCounting: Long,

    /**
     * 作业作业状态：WorkStatus
     */
    @ColumnInfo(name = "state")
    val state: Int,

    @ColumnInfo(name = "student_check_score")
    val studentCheckScore: Int,

    /**
     * 最近作业的一些异常状态，OCR 识别失败，AI 批改失败等等，方便后续回显，
     */
    @ColumnInfo(name = "error_message")
    var errorMessage:String?
)