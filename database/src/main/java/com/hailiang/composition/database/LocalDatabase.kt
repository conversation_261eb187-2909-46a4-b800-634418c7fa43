package com.hailiang.workcloud.database.room

import androidx.room.AutoMigration
import androidx.room.Database
import androidx.room.Room
import androidx.room.RoomDatabase
import androidx.room.migration.AutoMigrationSpec
import androidx.room.migration.Migration
import androidx.sqlite.db.SupportSQLiteDatabase
import com.hailiang.core.base.HlBaseApplication
import com.hailiang.core.db.SqliteContextWrapper
import com.hailiang.workcloud.database.room.dao.CompositionCheckDetailDao
import com.hailiang.workcloud.database.room.dao.CompositionPracticeDao
import com.hailiang.workcloud.database.room.dao.CompositionWorkAnswerDao
import com.hailiang.workcloud.database.room.dao.DoWorkStateDao
import com.hailiang.workcloud.database.room.dao.HandwriteHistoryDao
import com.hailiang.workcloud.database.room.pojo.CompositionCheckDetailPo
import com.hailiang.workcloud.database.room.pojo.CompositionPracticePo
import com.hailiang.workcloud.database.room.pojo.CompositionWorkAnswerPo
import com.hailiang.workcloud.database.room.pojo.DoWorkStatePo
import com.hailiang.workcloud.database.room.pojo.HandwriteHistoryPo


/**
 * Description:
 *
 * <AUTHOR>
 * @version 2024/7/17 16:59
 */
@Database(
    entities = [
        HandwriteHistoryPo::class,
        CompositionPracticePo::class,
        CompositionWorkAnswerPo::class,
        DoWorkStatePo::class,
        CompositionCheckDetailPo::class,
    ],
    version = 3,
    autoMigrations = [
        AutoMigration(from = 2, to = 3),
    ]
)
abstract class LocalDatabase : RoomDatabase() {
    companion object {
        /**
         * version 1 升级到version 2
         */
        private val MIGRATION_2_3 = object : Migration(1, 2) {
            override fun migrate(database: SupportSQLiteDatabase) {
//                database.execSQL("CREATE TABLE IF NOT EXISTS composition_work_answer (`id` INTEGER, `work_id` INTEGER NOT NULL, `first_answer` TEXT, `second_answer_detail` TEXT, `answer_edit_detail` TEXT, PRIMARY KEY(`id`))")
            }
        }


        val database by lazy(mode = LazyThreadSafetyMode.SYNCHRONIZED) {
            Room.databaseBuilder<LocalDatabase>(
                SqliteContextWrapper(base = HlBaseApplication.getInstance(), extra = true),
                LocalDatabase::class.java, "hl_student_work.db"
            )
                .allowMainThreadQueries()
//                .addMigrations(MIGRATION_1_2)
                .fallbackToDestructiveMigrationFrom(1)
                .build()
        }
    }

    /**
     * 手写历史
     */
    abstract fun getHandwriteHistoryDao(): HandwriteHistoryDao

    /**
     * 作答本地状态
     */
    abstract fun getDoWorkStateDao(): DoWorkStateDao

    /**
     * 议论文本地做题情况
     */
    abstract fun getCompositionLocalPracticeDao(): CompositionPracticeDao

    /**
     * 服务端返回的学生作答信息
     */
    abstract fun getCompositionWorkAnswerDao(): CompositionWorkAnswerDao

    /**
     * 作文批改情况
     */
    abstract fun getCompositionCheckDetailDao(): CompositionCheckDetailDao


}