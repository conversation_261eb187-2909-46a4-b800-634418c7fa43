package com.hailiang.workcloud.database.room.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Description: 服务端返回到学生作文作答信息
 *
 * <AUTHOR>
 * @version 2025/2/25 21:10
 */
@Entity(tableName = "composition_work_answer")
data class CompositionWorkAnswerPo(

    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    @ColumnInfo(name = "work_id")
    val workId: Long,

    @ColumnInfo(name = "answer_id")
    val answerId: Long?,

    /**
     * 首次作答ocr结果
     */
    @ColumnInfo(name = "first_answer")
    val firstAnswer: String?,

    /**
     * 二次作答信息
     */
    @ColumnInfo(name = "second_answer_detail")
    val secondAnswerDetail: String?,

    @ColumnInfo(name = "answer_edit_detail")
    val answerEditDetail: String?,

    @ColumnInfo(name = "image_answer_list")
    val imageAnswerList: String?,

    /**
     * 2025-03-25 14:34:49
     */
    @ColumnInfo(name = "image_update_time")
    val imageUpdateTime: String?,
)