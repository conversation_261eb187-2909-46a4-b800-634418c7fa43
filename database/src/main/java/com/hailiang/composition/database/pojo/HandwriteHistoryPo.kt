package com.hailiang.workcloud.database.room.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

@Entity(tableName = "handwrite_history")
data class HandwriteHistoryPo(
    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    @ColumnInfo(name = "work_id")
    val workId: Long,

    @ColumnInfo(name = "question_id")
    val questionId: Long,

    @ColumnInfo(name = "sub_question_id")
    val subQuestionId: Long,

    @ColumnInfo(name = "task_id")
    val taskId: Long,

    @ColumnInfo(name = "material_id")
    val materialId: Long,

    @ColumnInfo(name = "idx")
    val idx: Int,

    /**
     * 外部传入标识，用于特殊的作业、例如开放性作业
     */
    @ColumnInfo(name = "tag")
    var tag: String,

    @ColumnInfo(name = "strokes")
    val strokes: String,

    @ColumnInfo(name = "update_time")
    val updateTime: Long,
)