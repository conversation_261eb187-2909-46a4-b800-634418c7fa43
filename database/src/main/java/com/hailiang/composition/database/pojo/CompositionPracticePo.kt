package com.hailiang.workcloud.database.room.pojo

import androidx.room.ColumnInfo
import androidx.room.Entity
import androidx.room.PrimaryKey

/**
 * Description: 本地 议论文作答记录表
 *
 * <AUTHOR>
 * @version 2025/2/27 19:12
 */
@Entity(tableName = "composition_practice")
data class CompositionPracticePo(
    @PrimaryKey(autoGenerate = true)
    val id: Long?,

    /**
     * 作业id：服务端
     */
    @ColumnInfo(name = "work_id")
    val workId: Long,

    /**
     * 作业状态id：服务端
     */
    @ColumnInfo(name = "work_state_id")
    val workStateId: Long,

    /**
     * 二次作答标题
     */
    @ColumnInfo(name = "second_practice_title")
    val secondPracticeTitle: String?,
    /**
     * 二次作答结果
     */
    @ColumnInfo(name = "second_practice_content")
    val secondPracticeContent: String?,
)