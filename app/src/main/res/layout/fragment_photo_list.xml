<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout
    xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    xmlns:tools="http://schemas.android.com/tools">

    <View
        android:id="@+id/view_close"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="100dp"/>

    <androidx.constraintlayout.widget.ConstraintLayout
        android:id="@+id/cl_content"
        android:layout_width="642dp"
        android:layout_height="match_parent"
        android:background="#B3000000"
        android:visibility="gone"
        android:paddingStart="20dp"
        tools:visibility="visible"
        app:layout_constraintEnd_toEndOf="parent">

        <ImageView
            android:id="@+id/btn_close_fragment"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_marginEnd="10dp"
            android:padding="10dp"
            android:src="@drawable/icon_close_photo"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="20dp"
            android:text="@string/title"
            android:textColor="@color/white"
            android:textSize="20sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rl_title"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="64dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />


        <ImageView
            android:id="@+id/iv_title_add"
            android:layout_width="102dp"
            android:layout_height="102dp"
            android:layout_marginTop="64dp"
            android:background="@drawable/icon_add_photo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:id="@+id/tv_composition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginTop="198dp"
            android:text="@string/composition"
            android:textColor="@color/white"
            android:textSize="20sp"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="12dp"
            android:text="@string/drag_item_hint"
            android:textColor="#80ffffff"
            android:textSize="20sp"
            app:layout_constraintStart_toEndOf="@id/tv_composition"
            app:layout_constraintTop_toTopOf="@id/tv_composition" />

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/rl_composition"
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_marginTop="16dp"
            android:layout_marginEnd="20dp"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_composition" />

        <ImageView
            android:id="@+id/iv_composition_add"
            android:layout_width="102dp"
            android:layout_height="102dp"
            android:layout_marginTop="32dp"
            android:background="@drawable/icon_add_photo"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toBottomOf="@id/tv_composition"/>


    </androidx.constraintlayout.widget.ConstraintLayout>


    <LinearLayout
        android:id="@+id/ll_panel"
        android:layout_width="146dp"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp">

            <ImageView
                android:id="@+id/iv_title_result"
                android:layout_width="102dp"
                android:layout_height="102dp"
                android:background="@drawable/icon_add_photo"
                android:padding="2dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />

            <com.hailiang.component.shape.GradientTextView
                android:id="@+id/view_type_title"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                app:layout_constraintTop_toTopOf="@id/iv_title_result"
                app:layout_constraintStart_toStartOf="@id/iv_title_result"
                app:layout_constraintEnd_toEndOf="@id/iv_title_result"
                app:layout_constraintBottom_toBottomOf="@id/iv_title_result"
                android:visibility="gone"
                app:cornerRadius="6dp"
                app:strokeColor="#6775FA"
                app:strokeWidth="2.5dp" />

            <com.hailiang.component.shape.GradientTextView
                android:layout_width="36dp"
                android:layout_height="19dp"
                android:layout_marginStart="2dp"
                android:layout_marginTop="2dp"
                android:gravity="center"
                android:text="@string/title"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:bottomRightRadius="10dp"
                app:layout_constraintStart_toStartOf="@id/iv_title_result"
                app:layout_constraintTop_toTopOf="@id/iv_title_result"
                app:solidColor="#99000000"
                app:topLeftRadius="6dp" />


            <com.hailiang.component.shape.GradientTextView
                android:id="@+id/tv_title_num"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginStart="-10dp"
                android:layout_marginBottom="-10dp"
                android:textColor="@color/white"
                android:textSize="16sp"
                app:cornerRadius="11dp"
                android:gravity="center"
                android:visibility="invisible"
                app:layout_constraintBottom_toTopOf="@id/iv_title_result"
                app:layout_constraintStart_toEndOf="@id/iv_title_result"
                app:solidColor="#6775FA" />


        </androidx.constraintlayout.widget.ConstraintLayout>

        <androidx.constraintlayout.widget.ConstraintLayout
            android:id="@+id/cl_composition"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="4dp"
            android:layout_marginTop="10dp">

            <ImageView
                android:id="@+id/iv_composition_result"
                android:layout_width="102dp"
                android:layout_height="102dp"
                android:background="@drawable/icon_add_photo"
                android:padding="1dp"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent" />
            <com.hailiang.component.shape.GradientTextView
                android:id="@+id/view_type_composition"
                android:layout_width="0dp"
                android:layout_height="0dp"
                android:gravity="center"
                app:layout_constraintTop_toTopOf="@id/iv_composition_result"
                app:layout_constraintStart_toStartOf="@id/iv_composition_result"
                app:layout_constraintEnd_toEndOf="@id/iv_composition_result"
                app:layout_constraintBottom_toBottomOf="@id/iv_composition_result"
                app:cornerRadius="6dp"
                app:strokeColor="#6775FA"
                app:strokeWidth="2.5dp"
                />

            <com.hailiang.component.shape.GradientTextView
                android:layout_width="36dp"
                android:layout_height="19dp"
                android:layout_marginStart="2dp"
                android:layout_marginTop="2dp"
                android:gravity="center"
                android:text="@string/composition"
                android:textColor="@color/white"
                android:textSize="12sp"
                app:bottomRightRadius="10dp"
                app:layout_constraintStart_toStartOf="@id/iv_composition_result"
                app:layout_constraintTop_toTopOf="@id/iv_composition_result"
                app:solidColor="#99000000"
                app:topLeftRadius="6dp" />


            <com.hailiang.component.shape.GradientTextView
                android:id="@+id/tv_composition_num"
                android:layout_width="22dp"
                android:layout_height="22dp"
                android:layout_marginStart="-10dp"
                android:layout_marginBottom="-10dp"
                android:textColor="@color/white"
                android:visibility="invisible"
                android:textSize="16sp"
                app:cornerRadius="11dp"
                android:gravity="center"
                app:layout_constraintBottom_toTopOf="@id/iv_composition_result"
                app:layout_constraintStart_toEndOf="@id/iv_composition_result"
                app:solidColor="#6775FA" />

        </androidx.constraintlayout.widget.ConstraintLayout>

        <com.hailiang.component.shape.GradientTextView
            android:id="@+id/btn_correct"
            android:layout_width="106dp"
            android:layout_height="43dp"
            android:layout_marginTop="24dp"
            android:layout_marginBottom="21dp"
            android:gravity="center"
            android:text="@string/to_correct"
            android:textColor="@color/white"
            android:textSize="16sp"
            app:cornerRadius="22dp"
            app:solidColor="#6775FA" />

    </LinearLayout>


</androidx.constraintlayout.widget.ConstraintLayout>