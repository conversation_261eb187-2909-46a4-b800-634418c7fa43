<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <com.hailiang.component.shape.GradientTextView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:gradientEndColor="#E3F3FF"
        app:gradientOrientation="LEFT_RIGHT"
        app:gradientStartColor="#DFE5FF" />

    <de.hdodenhof.circleimageview.CircleImageView
        android:id="@+id/iv_avatar"
        android:layout_width="46dp"
        android:layout_height="46dp"
        android:layout_marginStart="36dp"
        android:layout_marginTop="24dp"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:src="@tools:sample/avatars" />

    <TextView
        android:id="@+id/tv_name"
        style="@style/Title.Big"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="16dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintLeft_toRightOf="@id/iv_avatar"
        app:layout_constraintTop_toTopOf="@id/iv_avatar" />

    <com.hailiang.component.FixedDrawableSizeTextView
        android:id="@+id/tv_time"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="36dp"
        android:drawableLeft="@drawable/alarm"
        android:drawablePadding="8dp"
        android:gravity="center_vertical"
        android:textColor="#515A6E"
        android:textSize="14sp"
        app:drawableHeight="16dp"
        app:drawableWidth="16dp"
        app:layout_constraintBottom_toBottomOf="@id/iv_avatar"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/iv_avatar"
        tools:ignore="RtlHardcoded" />

    <FrameLayout
        android:id="@+id/ll_write"
        android:layout_width="260dp"
        android:layout_height="68dp"
        android:layout_marginBottom="30dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintRight_toRightOf="parent">

        <com.hailiang.component.shape.GradientTextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            app:cornerRadius="99dp"
            app:solidColor="#6775FA" />

        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal"
            android:layout_marginTop="10dp"
            android:gravity="center_vertical"
            android:orientation="horizontal"
            tools:ignore="UseCompoundDrawables">

            <ImageView
                android:layout_width="20dp"
                android:layout_height="20dp"
                android:importantForAccessibility="no"
                android:src="@drawable/add" />

            <TextView
                android:id="@+id/tv_write"
                style="@style/Title.Medium"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="6dp"
                android:text="作文"
                android:textColor="@color/white"
                tools:ignore="HardcodedText" />
        </LinearLayout>

        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_horizontal|bottom"
            android:layout_marginBottom="12dp"
            android:text="仅支持议论文"
            android:textColor="@color/white_opaque_60"
            android:textSize="11sp"
            tools:ignore="HardcodedText" />
    </FrameLayout>

    <com.hailiang.component.FixedDrawableSizeTextView
        android:id="@+id/tv_photo_example"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="36dp"
        android:drawableLeft="@drawable/ic_take_photo"
        android:drawablePadding="6dp"
        android:text="拍照示例"
        android:textColor="#6775FA"
        android:textSize="18sp"
        app:drawableHeight="24dp"
        app:drawableWidth="24dp"
        app:layout_constraintBottom_toBottomOf="@id/ll_write"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="@id/ll_write"
        tools:ignore="HardcodedText,RtlHardcoded" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginHorizontal="36dp"
        android:layout_marginTop="90dp"
        app:layout_constraintTop_toTopOf="parent"
        app:layout_constraintBottom_toBottomOf="parent"
        android:layout_marginBottom="112dp">

        <ViewStub
            android:id="@+id/vs_empty"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clickable="true"
            android:layout="@layout/view_composition_empty" />

        <com.hailiang.xxb.refresh.SmartRefreshLayout
            android:id="@+id/smart_refresh"
            android:layout_width="match_parent"
            android:layout_height="match_parent">

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/rv"
                android:layout_width="match_parent"
                android:layout_height="match_parent" />

        </com.hailiang.xxb.refresh.SmartRefreshLayout>
    </FrameLayout>
</androidx.constraintlayout.widget.ConstraintLayout>