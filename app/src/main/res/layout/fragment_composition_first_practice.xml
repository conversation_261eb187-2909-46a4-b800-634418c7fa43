<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="36dp"
    android:layout_marginTop="20dp"
    android:layout_marginBottom="36dp"
    android:background="@drawable/bg_white_radius_22dp"
    android:paddingHorizontal="28dp">

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_marginBottom="40dp"
        android:fadingEdge="none"
        android:overScrollMode="never"
        android:scrollbars="none"
        app:layout_constraintBottom_toTopOf="@+id/btnSubmit"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content">

            <View
                android:layout_width="0dp"
                android:layout_height="10dp"
                android:background="@drawable/ic_first_title_bg"
                app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
                app:layout_constraintEnd_toEndOf="@+id/tvTitle"
                app:layout_constraintStart_toStartOf="@+id/tvTitle" />

            <TextView
                android:id="@+id/tvTitle"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_20"
                android:text="作文题目"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent" />


            <LinearLayout
                android:id="@+id/llRemark"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_68"
                android:background="@drawable/bg_f5f6fd_radius20"
                android:orientation="vertical"
                android:padding="20dp"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintHeight_max="@dimen/composition_question_content_max_height"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent">

                <TextView
                    android:id="@+id/tvRemark"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:textColor="#0A0A0A"
                    android:textSize="18sp" />

                <LinearLayout
                    android:id="@+id/llTaskMaterial"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="20dp"
                    android:orientation="horizontal"
                    android:visibility="gone" />

            </LinearLayout>

            <TextView
                android:id="@+id/tvDeadline"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_14"
                android:textColor="#9094A1"
                android:textSize="18sp"
                android:textStyle="normal"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/llRemark"
                tools:text="05-15 23:59:59 截止" />

            <CheckBox
                android:id="@+id/expand_cb"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:background="@null"
                android:button="@null"
                android:checked="false"
                android:drawableLeft="@drawable/ic_checkbox"
                android:drawablePadding="8dp"
                android:text="展开"
                android:textColor="#FF6775FA"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold"
                app:layout_constraintBottom_toBottomOf="@id/tvDeadline"
                app:layout_constraintRight_toRightOf="parent"
                app:layout_constraintTop_toTopOf="@id/tvDeadline" />

            <View
                android:id="@+id/bgMyAnswer"
                android:layout_width="0dp"
                android:layout_height="10dp"
                android:background="@drawable/ic_first_title_bg"
                app:layout_constraintBottom_toBottomOf="@+id/tvMyAnswer"
                app:layout_constraintEnd_toEndOf="@+id/tvMyAnswer"
                app:layout_constraintStart_toStartOf="@+id/tvMyAnswer" />

            <TextView
                android:id="@+id/tvMyAnswer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="@dimen/dp_24"
                android:text="我的作答"
                android:textColor="@color/black"
                android:textSize="24sp"
                android:textStyle="bold"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvDeadline"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/takePhotoGuideBtn"
                android:layout_width="wrap_content"
                android:layout_height="38dp"
                android:layout_marginStart="@dimen/dp_16"
                android:drawableStart="@drawable/ic_take_photo"
                android:drawablePadding="@dimen/dp_8"
                android:gravity="center"
                android:text="拍照示例"
                android:textColor="#FF6775FA"
                android:textSize="@dimen/sp_18"
                android:textStyle="bold"
                app:layout_constraintStart_toEndOf="@+id/tvMyAnswer"
                app:layout_constraintTop_toTopOf="@+id/tvMyAnswer"
                tools:visibility="visible" />

            <androidx.recyclerview.widget.RecyclerView
                android:id="@+id/recycler"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginTop="8dp"
                android:clipChildren="false"
                android:overScrollMode="never"
                app:layout_constraintEnd_toEndOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/tvMyAnswer" />

            <TextView
                android:id="@+id/tvErrorInto"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginTop="16dp"
                android:textColor="#FF0000"
                android:textSize="16sp"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toBottomOf="@+id/recycler"
                tools:text="上传失败，请重新上传" />


            <androidx.constraintlayout.widget.Group
                android:id="@+id/groupMyAnswer"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:visibility="invisible"
                app:constraint_referenced_ids="tvMyAnswer,recycler,bgMyAnswer,tvErrorInto,expand_cb,takePhotoGuideBtn"
                tools:visibility="visible" />

        </androidx.constraintlayout.widget.ConstraintLayout>
    </ScrollView>

    <TextView
        android:id="@+id/btnSubmit"
        android:layout_width="300dp"
        android:layout_height="56dp"
        android:layout_marginBottom="30dp"
        android:background="@drawable/ic_first_submit_bg"
        android:gravity="center"
        android:text="提交作答"
        android:textColor="@color/white"
        android:textSize="20sp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent" />


</androidx.constraintlayout.widget.ConstraintLayout>