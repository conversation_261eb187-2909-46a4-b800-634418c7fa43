<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:contentDescription="@null"
    android:scrollbars="none">

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:paddingVertical="@dimen/dp_10">

        <com.hailiang.view.question.composition.CompositionTableLayout
            android:id="@+id/composition_table_layout"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_gravity="top"
            android:layout_marginHorizontal="@dimen/dp_10"
            android:layout_marginBottom="@dimen/dp_100"
            tools:minHeight="1000dp" />

        <TextView
            android:id="@+id/composition_score_tv"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="top|right"
            android:layout_marginRight="@dimen/dp_20"
            android:textColor="#FFEB5848"
            android:textSize="@dimen/sp_58"
            android:textStyle="bold"
            android:visibility="gone"
            tools:text="51分"
            tools:visibility="visible" />
    </FrameLayout>
</ScrollView>