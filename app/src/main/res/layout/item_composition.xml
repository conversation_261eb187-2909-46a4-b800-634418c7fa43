<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="389dp"
    android:layout_height="530dp"
    android:background="@color/white">

    <LinearLayout
        android:id="@+id/ll_title"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginStart="16dp"
        android:gravity="center_vertical"
        android:orientation="horizontal"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <com.airbnb.lottie.LottieAnimationView
            android:id="@+id/lottie"
            android:layout_width="30dp"
            android:layout_height="30dp"
            app:lottie_repeatCount="-1" />

        <TextView
            android:id="@+id/tv_title"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:maxWidth="220dp"
            android:singleLine="true"
            android:textColor="#0A0A0A"
            android:textSize="16sp"
            android:textStyle="bold"
            app:layout_constraintLeft_toLeftOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:text="作文标题" />
    </LinearLayout>

    <TextView
        android:id="@+id/tv_score"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginEnd="16dp"
        android:layout_marginBottom="6dp"
        android:textColor="#6775FA"
        android:textSize="16sp"
        app:layout_constraintBottom_toTopOf="@id/iv_img"
        app:layout_constraintRight_toRightOf="parent"
        tools:text="48" />

    <FrameLayout
        android:layout_width="match_parent"
        android:layout_height="40dp"
        android:layout_marginBottom="2dp"
        app:layout_constraintBottom_toBottomOf="parent">

        <com.hailiang.component.shape.GradientTextView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:layout_marginHorizontal="2dp"
            app:bottomLeftRadius="15dp"
            app:bottomRightRadius="15dp"
            app:solidColor="#F3F6FF"
            app:topLeftRadius="2dp"
            app:topRightRadius="2dp" />

        <com.hailiang.component.FixedDrawableSizeTextView
            android:id="@+id/tv_time"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="14dp"
            android:drawableLeft="@drawable/clock"
            android:drawablePadding="6dp"
            android:textColor="#515A6E"
            android:textSize="14sp"
            app:drawableHeight="15dp"
            app:drawableWidth="15dp"
            tools:ignore="HardcodedText,RtlHardcoded"
            tools:text="2025/05/16" />

        <com.hailiang.component.FixedDrawableSizeTextView
            android:id="@+id/tv_action"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="64dp"
            android:drawableLeft="@drawable/check_report"
            android:drawablePadding="6dp"
            android:textColor="#6775FA"
            android:textSize="16sp"
            android:textStyle="bold"
            app:drawableHeight="18dp"
            app:drawableWidth="18dp"
            tools:ignore="HardcodedText,RtlHardcoded"
            tools:text="@string/check_report" />

        <ImageView
            android:id="@+id/iv_delete"
            android:layout_width="40dp"
            android:layout_height="40dp"
            android:layout_gravity="center_vertical|end"
            android:layout_marginEnd="3dp"
            android:importantForAccessibility="no"
            android:padding="11dp"
            android:src="@drawable/delete_report" />
    </FrameLayout>

    <ImageView
        android:id="@+id/iv_img"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginHorizontal="16dp"
        android:layout_marginVertical="44dp"
        android:background="@color/black_opaque_8"
        android:contentDescription="@null" />
</androidx.constraintlayout.widget.ConstraintLayout>