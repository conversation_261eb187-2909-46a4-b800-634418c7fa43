<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="102dp"
    android:layout_height="102dp">

    <ImageView
        android:id="@+id/iv_photo"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:padding="1dp"
        android:background="@drawable/icon_add_photo" />

    <ImageView
        android:id="@+id/btn_delete"
        android:layout_width="24dp"
        android:layout_height="24dp"
        android:layout_margin="1dp"
        android:background="@drawable/bg_60black_06010radius"
        android:padding="6dp"
        android:src="@drawable/icon_close_photo"
        android:visibility="gone"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!--	遮罩-->
    <View
            android:id="@+id/vMask"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:background="@color/black"
            android:alpha="0.5"
            android:visibility="gone"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent" />

    <!-- 上传中提示 -->
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent">

        <ImageView
                android:id="@+id/ivUploading"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:src="@drawable/icon_upload_loading"
                android:visibility="gone" />

        <TextView
                android:id="@+id/tvUploading"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上传中..."
                android:textColor="@color/white"
                android:textSize="14sp"
                android:padding="4dp"
                android:visibility="gone" />
    </LinearLayout>
    <!-- 上传失败提示 -->
    <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            app:layout_constraintBottom_toBottomOf="parent"
            app:layout_constraintEnd_toEndOf="parent"
            app:layout_constraintStart_toStartOf="parent"
            app:layout_constraintTop_toTopOf="parent"
            tools:ignore="MissingConstraints">

        <ImageView
                android:id="@+id/ivUploadFailed"
                android:layout_width="42dp"
                android:layout_height="42dp"
                android:src="@drawable/icon_upload_failed"
                android:visibility="gone" />

        <TextView
                android:id="@+id/tvUploadFailed"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="上传失败"
                android:textColor="@color/white"
                android:textSize="14sp"
                android:padding="2dp"
                android:visibility="gone" />

        <TextView
                android:id="@+id/btnRetry"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="重试"
                android:textColor="@color/black"
                android:textFontWeight="500"
                android:textSize="14sp"
                android:background="@drawable/bg_22radius_white"
                android:paddingStart="22dp"
                android:paddingEnd="22dp"
                android:paddingTop="3dp"
                android:paddingBottom="3dp"
                android:visibility="gone" />
    </LinearLayout>

</androidx.constraintlayout.widget.ConstraintLayout>