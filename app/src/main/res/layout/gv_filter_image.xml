<?xml version="1.0" encoding="utf-8"?>
<com.luck.picture.lib.widget.SquareRelativeLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:clipChildren="false">

    <ImageView
        android:id="@+id/fiv"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_f5f5f5_radius4" />

    <View
        android:id="@+id/vMask"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:background="@drawable/bg_bebfc6_radius4"
        android:visibility="gone" />

    <FrameLayout
        android:id="@+id/iv_del"
        android:layout_width="36dp"
        android:layout_height="36dp"
        android:layout_alignTop="@id/fiv"
        android:layout_alignRight="@id/fiv"
        android:layout_marginTop="-15dp"
        android:layout_marginEnd="-15dp"
        android:visibility="gone"
        tools:visibility="visible">

        <ImageView
            android:layout_width="24dp"
            android:layout_height="24dp"
            android:layout_gravity="center"
            android:scaleType="centerInside"
            android:src="@drawable/icon_x" />
    </FrameLayout>


    <ImageView
        android:id="@+id/ivUploading"
        android:layout_width="92dp"
        android:layout_height="92dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvUploading"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ivUploading"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:text="上传中"
        android:textColor="#0A0A0A"
        android:textSize="18sp"
        android:visibility="gone" />

    <ImageView
        android:id="@+id/ivUploadFailed"
        android:layout_width="92dp"
        android:layout_height="92dp"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:src="@drawable/ic_upload_failed"
        android:visibility="gone" />

    <TextView
        android:id="@+id/tvUploadFailed"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_below="@id/ivUploadFailed"
        android:layout_centerHorizontal="true"
        android:text="上传失败"
        android:textColor="#0A0A0A"
        android:textSize="18sp"
        android:visibility="gone" />

    <TextView
        android:id="@+id/btnRetry"
        android:layout_width="108dp"
        android:layout_height="30dp"
        android:layout_below="@+id/tvUploadFailed"
        android:layout_centerHorizontal="true"
        android:layout_marginTop="10dp"
        android:background="@drawable/bg_stroke6775fa_radius28"
        android:gravity="center"
        android:text="重试"
        android:textColor="#6372FD"
        android:visibility="gone" />


</com.luck.picture.lib.widget.SquareRelativeLayout>