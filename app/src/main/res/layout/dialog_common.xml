<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    android:layout_width="500dp"
    android:layout_height="wrap_content"
    android:background="@drawable/bg_white_radius20"
    android:orientation="vertical"
    android:padding="30dp">

    <TextView
        android:id="@+id/dialog_title"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center"
        android:paddingBottom="16dp"
        android:textColor="#D9000000"
        android:textSize="20sp"
        android:textStyle="bold" />

    <LinearLayout
        android:id="@+id/content_container"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginTop="10dp"
        android:gravity="center_horizontal"
        android:orientation="vertical"
        android:minHeight="60dp" />

    <LinearLayout
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="30dp"
        android:layout_gravity="center_horizontal"
        android:orientation="horizontal">

        <TextView
            android:id="@+id/dialog_cancel"
            android:layout_width="135dp"
            android:layout_height="43dp"
            android:layout_marginEnd="20dp"
            android:layout_marginRight="16dp"
            android:background="@drawable/bg_stroke_e0e0e0_radius21"
            android:gravity="center"
            android:text="取消"
            android:textColor="#A6000000"
            android:textSize="16sp" />

        <TextView
            android:id="@+id/dialog_confirm"
            android:layout_width="135dp"
            android:layout_height="43dp"
            android:background="@drawable/bg_1759ee_radius21"
            android:gravity="center"
            android:text="确定"
            android:textColor="@color/white"
            android:textSize="16sp" />
    </LinearLayout>

</LinearLayout>