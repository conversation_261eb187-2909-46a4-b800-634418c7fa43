<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <FrameLayout
        android:id="@+id/composition_student_images_container"
        android:layout_width="@dimen/composition_student_image_container_width"
        android:layout_height="0dp"
        android:layout_marginStart="@dimen/dp_24"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginBottom="@dimen/dp_24"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintLeft_toLeftOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <LinearLayout
        android:id="@+id/ll_composition_student_content_container"
        android:layout_width="@dimen/composition_student_image_container_width"
        android:layout_height="match_parent"
        android:layout_marginTop="@dimen/dp_6"
        android:layout_marginEnd="@dimen/dp_24"
        android:layout_marginBottom="@dimen/dp_24"
        android:background="@color/white"
        android:orientation="vertical"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <FrameLayout
            android:id="@+id/composition_student_content_container"
            android:layout_width="match_parent"
            android:layout_height="0dp"
            android:layout_weight="1"
            android:contentDescription="@null"
            android:scrollbars="none" />


        <androidx.constraintlayout.widget.ConstraintLayout
            android:layout_width="match_parent"
            android:layout_height="@dimen/dp_48">

            <ImageView
                android:id="@+id/composition_word_count_iv"
                android:layout_width="@dimen/dp_18"
                android:layout_height="@dimen/dp_18"
                android:layout_marginStart="@dimen/dp_16"
                android:layout_marginBottom="@dimen/dp_6"
                android:src="@drawable/ic_count_tag"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="parent"
                app:layout_constraintStart_toStartOf="parent"
                app:layout_constraintTop_toTopOf="parent"
                tools:visibility="visible" />

            <TextView
                android:id="@+id/composition_word_count_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="4dp"
                android:includeFontPadding="false"
                android:textColor="@color/app_info_text_color"
                android:textSize="@dimen/sp_18"
                app:layout_constraintBottom_toBottomOf="@id/composition_word_count_iv"
                app:layout_constraintLeft_toRightOf="@id/composition_word_count_iv"
                app:layout_constraintTop_toTopOf="@id/composition_word_count_iv"
                tools:text="共802字" />

            <TextView
                android:id="@+id/composition_save_tip_tv"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="24dp"
                android:text="已自动暂存"
                android:textColor="@color/app_info_text_color"
                android:textSize="@dimen/sp_18"
                android:visibility="gone"
                app:layout_constraintBottom_toBottomOf="@id/composition_word_count_iv"
                app:layout_constraintStart_toEndOf="@+id/composition_word_count_tv"
                app:layout_constraintTop_toTopOf="@id/composition_word_count_iv"
                tools:visibility="visible" />
        </androidx.constraintlayout.widget.ConstraintLayout>


    </LinearLayout>

<!--    <ImageView-->
<!--            android:id="@+id/composition_word_mark"-->
<!--            android:layout_width="58dp"-->
<!--            android:layout_height="31dp"-->
<!--            android:layout_marginStart="-8.dp"-->
<!--            android:layout_marginTop="20dp"-->
<!--            android:elevation="4dp"-->
<!--            android:src="@drawable/icon_second_mark"-->
<!--            app:layout_constraintStart_toStartOf="@id/ll_composition_student_content_container"-->
<!--            app:layout_constraintTop_toTopOf="@id/ll_composition_student_content_container"-->
<!--            tools:visibility="visible" />-->
    <ImageView
        android:id="@+id/composition_submit_btn"
        android:layout_width="@dimen/dp_86"
        android:layout_height="@dimen/dp_86"
        android:layout_marginEnd="@dimen/dp_10"
        android:layout_marginBottom="@dimen/dp_10"
        android:contentDescription="@null"
        android:src="@drawable/btn_submit_circle"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintRight_toRightOf="parent" />


    <FrameLayout
        android:id="@+id/composition_submit_success_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:visibility="gone" />
</androidx.constraintlayout.widget.ConstraintLayout>