<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@android:color/black">
    

    <androidx.camera.view.PreviewView
        android:id="@+id/viewFinder"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginEnd="152dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:scaleType="fillCenter" />
    
    <ImageView
        android:id="@+id/btn_close"
        android:layout_width="44dp"
        android:layout_height="44dp"
        android:padding="10dp"
        android:src="@drawable/icon_close_photo"
        android:layout_margin="10dp"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"/>

    <TextView
        android:id="@+id/tv_hint"
        android:layout_width="wrap_content"
        android:layout_height="50dp"
        android:layout_marginTop="14dp"
        android:background="@drawable/bg_black_15radius"
        android:gravity="center"
        android:paddingHorizontal="30dp"
        android:text="请确保图片朝上"
        android:textColor="#99ffffff"
        android:textSize="14sp"
        android:textStyle="normal"
        app:layout_constraintBottom_toBottomOf="@+id/viewFinder"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintStart_toStartOf="parent" />

    <RadioGroup
        android:id="@+id/radioGroupType"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:background="@drawable/bg_radio_group"
        android:orientation="vertical"
        android:padding="4dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toStartOf="@id/btnTakePicture"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/rbTitle"
            android:layout_width="40dp"
            android:layout_height="100dp"
            android:button="@null"
            android:checked="true"
            android:gravity="center"
            android:text="题\n目"
            android:textColor="@color/radio_button_text_color"
            android:textSize="20sp"
            android:textStyle="bold" />

        <RadioButton
            android:id="@+id/rbComposition"
            android:layout_width="40dp"
            android:layout_height="100dp"
            android:button="@null"
            android:gravity="center"
            android:text="作\n文"
            android:textColor="@color/radio_button_text_color"
            android:textSize="20sp"
            android:textStyle="bold" />
    </RadioGroup>


    <ImageButton
        android:id="@+id/btnTakePicture"
        android:layout_width="80dp"
        android:layout_height="80dp"
        android:layout_marginEnd="16dp"
        android:background="@android:color/transparent"
        android:scaleType="fitCenter"
        android:src="@drawable/btn_take_photo"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <RadioGroup
        android:id="@+id/radioGroupColumns"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginTop="20dp"
        android:background="@drawable/bg_radio_group"
        android:orientation="horizontal"
        android:padding="4dp"
        app:layout_constraintEnd_toEndOf="@id/viewFinder"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent">

        <RadioButton
            android:id="@+id/radioButtonSingle"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:button="@null"
            android:gravity="center"
            android:text="单栏"
            android:textColor="@color/radio_button_text_color"
            android:textSize="20sp"
            android:textStyle="bold" />

        <RadioButton
            android:id="@+id/radioButtonDouble"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:button="@null"
            android:gravity="center"
            android:checked="true"
            android:text="双栏"
            android:textColor="@color/radio_button_text_color"
            android:textSize="20sp"
            android:textStyle="bold" />

        <RadioButton
            android:id="@+id/radioButtonTriple"
            android:layout_width="70dp"
            android:layout_height="40dp"
            android:button="@null"
            android:gravity="center"
            android:text="三栏"
            android:textColor="@color/radio_button_text_color"
            android:textSize="20sp"
            android:textStyle="bold" />
    </RadioGroup>

    <com.hailiang.camera.takephoto.view.DashedLineView
        android:id="@+id/gridVertical1"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="65dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintHorizontal_bias="0.33"
        app:layout_constraintStart_toStartOf="@+id/viewFinder" />

    <com.hailiang.camera.takephoto.view.DashedLineView
        android:id="@+id/gridVertical3"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="65dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintHorizontal_bias="0.5"
        app:layout_constraintStart_toStartOf="@+id/viewFinder" />

    <com.hailiang.camera.takephoto.view.DashedLineView
        android:id="@+id/gridVertical2"
        android:layout_width="3dp"
        android:layout_height="match_parent"
        android:layout_marginVertical="65dp"
        android:visibility="invisible"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintHorizontal_bias="0.66"
        app:layout_constraintStart_toStartOf="@+id/viewFinder" />

    <TextView
        android:id="@+id/tv0"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="visible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv1"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/gridVertical3"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv2"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="2"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintStart_toStartOf="@+id/gridVertical3"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv3"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="1"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/gridVertical1"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv4"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="2"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/gridVertical2"
        app:layout_constraintStart_toStartOf="@+id/gridVertical1"
        app:layout_constraintTop_toTopOf="parent" />

    <TextView
        android:id="@+id/tv5"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="3"
        android:textColor="#ffffff"
        android:textSize="80sp"
        android:textStyle="bold"
        android:visibility="invisible"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="@+id/viewFinder"
        app:layout_constraintStart_toStartOf="@+id/gridVertical2"
        app:layout_constraintTop_toTopOf="parent" />


    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupTwoPart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="gridVertical3,tv1,tv2" />

    <androidx.constraintlayout.widget.Group
        android:id="@+id/groupThreePart"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="invisible"
        app:constraint_referenced_ids="gridVertical1,gridVertical2,tv3,tv4,tv5" />


    <FrameLayout
        android:id="@+id/fl_fragment"
        android:layout_width="0dp"
        android:layout_height="0dp"
        app:layout_constraintStart_toStartOf="@id/viewFinder"
        app:layout_constraintEnd_toEndOf="@id/viewFinder"
        app:layout_constraintTop_toTopOf="@id/viewFinder"
        app:layout_constraintBottom_toBottomOf="@id/viewFinder"/>


</androidx.constraintlayout.widget.ConstraintLayout>