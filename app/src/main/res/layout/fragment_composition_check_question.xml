<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:layout_marginHorizontal="36dp"
    android:layout_marginTop="20dp"
    android:layout_marginBottom="36dp"
    android:paddingHorizontal="28dp"
    android:paddingVertical="16dp">

    <com.hailiang.component.shape.GradientTextView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        app:cornerRadius="15dp"
        app:solidColor="@color/white" />

    <ScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:layout_marginTop="60dp"
        android:layout_marginBottom="16dp"
        android:layout_marginHorizontal="28dp">

        <LinearLayout
            android:id="@+id/ll_question_topic"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            app:layout_constraintTop_toTopOf="parent" />
    </ScrollView>

    <FrameLayout
        android:id="@+id/composition_ai_content_container"
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:contentDescription="@null"
        android:scrollbars="none" />

    <View
        android:layout_width="0dp"
        android:layout_height="10dp"
        android:background="@drawable/ic_first_title_bg"
        app:layout_constraintBottom_toBottomOf="@+id/tvTitle"
        app:layout_constraintEnd_toEndOf="@+id/tvTitle"
        app:layout_constraintStart_toStartOf="@+id/tvTitle" />

    <TextView
        android:id="@+id/tvTitle"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_marginStart="28dp"
        android:layout_marginTop="20dp"
        android:text="作文题目"
        android:textColor="@color/black"
        android:textSize="24sp"
        android:textStyle="bold"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent"
        tools:ignore="HardcodedText" />
</androidx.constraintlayout.widget.ConstraintLayout>