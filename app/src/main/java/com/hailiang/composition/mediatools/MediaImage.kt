package com.hailiang.composition.mediatools

import com.hailiang.camera.pictureselector.entity.HLLocalMedia


data class MediaImage(
    var state: Int = STATE_DEFAULT,
    val type: Int = 0,
    val media: HLLocalMedia,
    var url: String? = null,
) {
    companion object {
        const val STATE_DEFAULT = 0
        const val STATE_LOADING = 1
        const val STATE_FAILURE = 2
        const val STATE_SUCCESS = 3
    }
}
