package com.hailiang.composition.mediatools

import androidx.recyclerview.widget.ItemTouchHelper
import androidx.recyclerview.widget.RecyclerView
import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.AnimatorSet
import android.graphics.Canvas
import android.view.View
import android.view.animation.LinearInterpolator

class DragItemTouchHelperCallback(
    private val adapter: RecyclerView.Adapter<*>,
    private val onChange: () -> Unit,
) : ItemTouchHelper.Callback() {

    private var needScaleBig = true
    private var needScaleSmall = false

    override fun isLongPressDragEnabled(): Boolean = true

    override fun onSwiped(viewHolder: RecyclerView.ViewHolder, direction: Int) {
        // 不支持滑动删除
    }

    override fun getMovementFlags(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder): Int {
        return makeMovementFlags(
            ItemTouchHelper.DOWN or ItemTouchHelper.UP or ItemTouchHelper.LEFT or ItemTouchHelper.RIGHT,
            0
        )
    }

    override fun onMove(
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        target: RecyclerView.ViewHolder
    ): Boolean {
        val fromPosition = viewHolder.absoluteAdapterPosition
        val toPosition = target.absoluteAdapterPosition

        if (fromPosition < 0 || toPosition < 0 || fromPosition == toPosition) return true

        val itemViewType = target.itemViewType
        if (itemViewType != GridImageAdapter.TYPE_CAMERA) {
            val data = (adapter as? GridImageAdapter)?.data
            if (data != null) {
                if (fromPosition < toPosition) {
                    for (i in fromPosition until toPosition) {
                        data.add(i + 1, data.removeAt(i))
                    }
                } else {
                    for (i in fromPosition downTo toPosition + 1) {
                        data.add(i - 1, data.removeAt(i))
                    }
                }
                adapter.notifyItemMoved(fromPosition, toPosition)
                onChange()
            }
        }
        return true
    }

    override fun onChildDraw(
        c: Canvas,
        recyclerView: RecyclerView,
        viewHolder: RecyclerView.ViewHolder,
        dX: Float,
        dY: Float,
        actionState: Int,
        isCurrentlyActive: Boolean
    ) {
        val itemViewType = viewHolder.itemViewType
        if (itemViewType != GridImageAdapter.TYPE_CAMERA) {
            if (needScaleBig) {
                needScaleBig = false
                startScaleAnimation(viewHolder.itemView, 1.0f, 1.1f) {
                    needScaleSmall = true
                }
            }
            super.onChildDraw(c, recyclerView, viewHolder, dX, dY, actionState, isCurrentlyActive)
        }
    }

    override fun onSelectedChanged(viewHolder: RecyclerView.ViewHolder?, actionState: Int) {
        val itemViewType = viewHolder?.itemViewType ?: GridImageAdapter.TYPE_CAMERA
        if (itemViewType != GridImageAdapter.TYPE_CAMERA) {
            viewHolder?.itemView?.alpha = 0.7f
            super.onSelectedChanged(viewHolder, actionState)
        }
    }

    override fun clearView(recyclerView: RecyclerView, viewHolder: RecyclerView.ViewHolder) {
        val itemViewType = viewHolder.itemViewType
        if (itemViewType != GridImageAdapter.TYPE_CAMERA) {
            viewHolder.itemView.alpha = 1.0f
            if (needScaleSmall) {
                needScaleSmall = false
                startScaleAnimation(viewHolder.itemView, 1.1f, 1.0f) {
                    needScaleBig = true
                }
            }
            super.clearView(recyclerView, viewHolder)
            adapter.notifyItemChanged(viewHolder.absoluteAdapterPosition)
        }
    }

    private fun startScaleAnimation(
        view: View,
        from: Float,
        to: Float,
        onAnimationEnd: () -> Unit
    ) {
        val animatorSet = AnimatorSet()
        animatorSet.playTogether(
            ObjectAnimator.ofFloat(view, "scaleX", from, to),
            ObjectAnimator.ofFloat(view, "scaleY", from, to)
        )
        animatorSet.interpolator = LinearInterpolator()
        animatorSet.duration = 50
        animatorSet.start()
        animatorSet.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onAnimationEnd()
            }
        })
    }
}
