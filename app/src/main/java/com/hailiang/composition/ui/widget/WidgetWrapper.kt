package com.hailiang.composition.ui.widget

import android.content.Context
import android.view.View
import android.view.ViewGroup
import androidx.annotation.ColorInt
import androidx.compose.foundation.layout.Box
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.compose.ui.viewinterop.NoOpUpdate
import com.hailiang.handwrite.bean.DrawingRecord
import com.hailiang.handwrite.tools.HandwriteToolsLayout
import com.hailiang.handwrite.tools.HandwriteType
import com.hailiang.handwrite.tools.LineType
import com.hailiang.handwrite.widget.MyHandWritingView
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.question.build.QuestionParam
import com.hailiang.question.build.QuestionViewFactory
import com.hailiang.question.util.HandWriteHelper
import com.hailiang.question.widget.sample.QuestionViewSample
import com.hailiang.xxb.composition.R
import kotlinx.coroutines.delay

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/22 09:40
 */
@Composable
fun QuestionViewWrapper(
    questionViewSample: QuestionViewSample,
    questionParam: QuestionParam,
    preUpdate: () -> Unit,
    afterUpdated: () -> Unit,
) {
    LaunchedEffect(questionParam) {
        preUpdate()
        delay(20)
        questionViewSample.update(questionParam)
        afterUpdated()
    }
    AndroidViewWrapper(
        view = questionViewSample.root
    )
}

@Composable
fun QuestionViewWrapper(
    questionParam: QuestionParam,
) {
    val context = LocalContext.current
    val questionViewSample by remember {
        mutableStateOf(
            QuestionViewFactory.createQuestionView(
                context = context,
            )
        )
    }
    QuestionViewWrapper(
        questionViewSample,
        questionParam,
        preUpdate = { },
        afterUpdated = { }
    )
}


@Composable
fun HandwriteToolsWrapper(
    modifier: Modifier = Modifier,
    handwriteToolsLayout: HandwriteToolsLayout,
    drawType: Int,
) {
    LaunchedEffect(drawType) {
        HLog.d(HTag.TAG, "HandwriteToolsWrapper: drawType = $drawType")
        HandWriteHelper.onDrawTypeChanged(handwriteToolsLayout, drawType)
    }
    AndroidViewWrapper(
        view = handwriteToolsLayout,
        modifier = modifier,
    )
}

@Composable
fun HandwriteToolsWrapper(
    modifier: Modifier = Modifier,
    drawType: Int,
    defaultHandwriteType: HandwriteType,
    @ColorInt defaultColor: Int,
    typeChangedListener: HandwriteToolsLayout.TypeChangedListener,
) {
    val context = LocalContext.current
    val handwriteToolsLayout by remember {
        mutableStateOf(
            HandwriteToolsLayout(context).also {
                val colors = context.resources.getIntArray(R.array.pen_colors)
                it.typeChangedListener = typeChangedListener
                it.setDefaultType(defaultHandwriteType)
                it.updateAllColor(defaultColor)
                it.updateLineType(LineType.Dashed)
                it.setPenColors(colors)
                it.setLineColors(colors)
            }
        )
    }
    HandwriteToolsWrapper(
        modifier = modifier,
        handwriteToolsLayout = handwriteToolsLayout,
        drawType = drawType
    )
}

// ----------------------------------------------------------------------
@Composable
fun <T : MyHandWritingView> HandwriteViewWrapper(
    modifier: Modifier = Modifier,
    handwriteView: T,
    drawingRecord: DrawingRecord?,
) {
    LaunchedEffect(drawingRecord) {
        handwriteView.drawingRecord = drawingRecord
    }
    AndroidViewWrapper(
        view = handwriteView,
        modifier = modifier,
    )
}

@Composable
fun <T : View> AndroidViewWithPreview(
    factory: (Context) -> T,
    modifier: Modifier = Modifier,
    update: (T) -> Unit = NoOpUpdate,
) {
    if (LocalInspectionMode.current) {
        // Preview 模式下显示占位符
        Box(modifier = modifier) {
            Text(
                text = "AndroidView is not supported in Preview",
                textAlign = TextAlign.Center
            )
        }
        return
    }
    AndroidView(
        factory = factory,
        modifier = modifier,
        update = update
    )
}


@Composable
fun <T : View> AndroidViewWrapper(
    view: T,
    modifier: Modifier = Modifier,
    update: (T) -> Unit = NoOpUpdate,
) {
    AndroidViewWithPreview(
        factory = {
            if (view.parent != null) {
                (view.parent as? ViewGroup)?.removeView(view)
            }
            view
        },
        modifier = modifier,
        update = update
    )
}