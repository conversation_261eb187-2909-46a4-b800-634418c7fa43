package com.hailiang.composition.ui.main

import android.annotation.SuppressLint
import android.text.style.ImageSpan
import android.view.View
import android.view.ViewGroup
import android.widget.ImageView
import android.widget.TextView
import androidx.constraintlayout.widget.ConstraintLayout
import androidx.constraintlayout.widget.Constraints
import androidx.core.graphics.toColorInt
import com.airbnb.lottie.LottieAnimationView
import com.bumptech.glide.Glide
import com.hailiang.component.FixedDrawableSizeTextView
import com.hailiang.composition.data.bean.WorkInfo
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.hlutil.SpannableBuilder
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.ext.inflate
import com.hailiang.hlutil.getColor
import com.hailiang.hlutil.getDrawable
import com.hailiang.hlutil.getString
import com.hailiang.hlutil.parseJson
import com.hailiang.hlutil.sp
import com.hailiang.recyclerview.adapter.ViewHolder
import com.hailiang.recyclerview.adapter.ViewPresenter
import com.hailiang.xxb.composition.R

class CompositionViewPresenter : ViewPresenter<WorkInfo>() {
    override fun onCreateViewHolder(parent: ViewGroup): ViewHolder {
        return ViewHolder(parent.inflate(R.layout.item_composition).apply {
            clipOutline(15.dp)
        })
    }

    @SuppressLint("SetTextI18n")
    override fun onBindViewHolder(
        holder: ViewHolder,
        data: WorkInfo,
        position: Int
    ) {
        val llTitle = holder.getView<View>(R.id.ll_title)
        val lottieView = holder.getView<LottieAnimationView>(R.id.lottie)
        val tvTitle = holder.getView<TextView>(R.id.tv_title)
        val tvScore = holder.getView<TextView>(R.id.tv_score)
        val tvTime = holder.getView<TextView>(R.id.tv_time)
        val tvAction = holder.getView<FixedDrawableSizeTextView>(R.id.tv_action)
        val ivImg = holder.getView<ImageView>(R.id.iv_img)

        val lp = llTitle.layoutParams as ConstraintLayout.LayoutParams
        when (data.ocrStatus) {
            JobStatus.FAILED.value -> {
                lp.rightToRight = Constraints.LayoutParams.UNSET
                lottieView.visibility = View.VISIBLE
                lottieView.setPadding(6.dpInt, 6.dpInt, 6.dpInt, 6.dpInt)
                lottieView.setImageDrawable(getDrawable(R.drawable.alert))
                tvTitle.setTextColor("#FD0002".toColorInt())
                tvTitle.text = "未正确识别"
            }

            JobStatus.RUNNING.value -> {
                lp.rightToRight = Constraints.LayoutParams.UNSET
                lottieView.visibility = View.VISIBLE
                lottieView.setPadding(0, 0, 0, 0)
                lottieView.setAnimation(R.raw.lottie_ai_identifying)
                lottieView.playAnimation()
                tvTitle.setTextColor("#6775FA".toColorInt())
                tvTitle.text = "识别中…"
            }

            else -> {
                lp.rightToRight = Constraints.LayoutParams.UNSET
                lottieView.visibility = View.GONE
                tvTitle.setTextColor("#0A0A0A".toColorInt())
                var compositionParagraphs: List<String>? = null
                if (!data.studentSecondAnswerDetail.isNullOrEmpty()) {
                    compositionParagraphs = parseJson<List<String>>(data.studentSecondAnswerDetail)
                } else if (!data.studentFirstAnswerDetail.isNullOrEmpty()) {
                    compositionParagraphs = parseJson<List<String>>(data.studentFirstAnswerDetail)
                }
                tvTitle.text = compositionParagraphs?.firstOrNull()
            }
        }
        llTitle.requestLayout()
        val teacherScore = data.teacherFirstCheckScore?.toFloat() ?: 0F
        val studentScore = data.studentCheckScore?.toFloat() ?: 0F
        if (teacherScore > 0) {
            val teacherScoreText = teacherScore.toString().removeSuffix(".0")
            val sb = SpannableBuilder.newInstance(teacherScoreText)
                .foregroundColor("#EB5848".toColorInt())
                .sizeInPx(18.sp.toInt())
                .bold()

            if (teacherScore > studentScore) {
                val drawable = getDrawable(R.drawable.score_improve)!!
                drawable.setBounds(0, 0, 6.dpInt, 9.dpInt)
                val scoreImprove = teacherScore.minus(studentScore).toString().removeSuffix(".0")
                sb.append(" ").sizeInPx(20)
                    .append(ImageSpan(drawable))
                    .append(" ").sizeInPx(5)
                    .append(scoreImprove)
                    .foregroundColor(getColor(com.hailiang.xxb.resource.R.color.black_opaque_65))
                    .sizeInPx(14.sp.toInt())
                    .bold()
            }
            tvScore.text = sb.build()
        } else if (studentScore > 0) {
            val text = studentScore.toString().removeSuffix(".0")
            tvScore.text = SpannableBuilder.newInstance(text)
                .foregroundColor("#EB5848".toColorInt())
                .bold()
                .sizeInPx(18.sp.toInt())
                .build()
        } else if (data.commentFirstStatus == JobStatus.SUCCESS.value) {
            tvScore.text = "生成中..."
            tvScore.setTextColor("#C4D6E8".toColorInt())
        } else {
            tvScore.text = ""
        }
        tvTime.text = if (data.createTime.contains(" ")) {
            data.createTime.split(" ").get(0)
        } else {
            data.createTime
        }
        if (data.status == 2 && data.commentFirstStatus == JobStatus.SUCCESS.value && studentScore > 0) {
            tvAction.text = getString(R.string.edit_composition)
            tvAction.setDrawableLeft(getDrawable(R.drawable.edit_composition))
            tvAction.visibility = View.VISIBLE
        } else {
            tvAction.text = getString(R.string.check_report)
            tvAction.setDrawableLeft(getDrawable(R.drawable.check_report))
            tvAction.visibility = View.VISIBLE
        }
        data.imgInfoList?.firstOrNull()?.answerPicUrl?.let { url ->
            Glide.with(ivImg.context).load(url)
                .into(ivImg)
        }
    }

    override fun onUnbindViewHolder(holder: ViewHolder) {
        super.onUnbindViewHolder(holder)
        val lottieView = holder.getView<LottieAnimationView>(R.id.lottie)
        lottieView.cancelAnimation()
        lottieView.clearAnimation()
        lottieView.setImageDrawable(null)
    }
}