package com.hailiang.composition.ui.widget

import android.graphics.BitmapFactory
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.border
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.asImageBitmap
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.CompositionQuestionImageState
import com.hailiang.composition.ui.practice.CompositionStudentAnswersBitmapState
import com.hailiang.hlutil.HLog
import com.hailiang.xxb.composition.R


private val STUDENT_IMAGE_CORNER_RADIUS = 12.dp
/**
 * Description: 学生作答图片
 *
 * <AUTHOR>
 * @version 2025/3/19 10:39
 */
@Composable
fun CompositionStudentImage(
    aiResponseState: AiResponseState,
    bitmapState: CompositionStudentAnswersBitmapState,
    imageModifier: Modifier = Modifier.padding(10.dp),
    questionsBitmap: CompositionQuestionImageState? = null,

    ) {
//    HLog.i("CompositionStudentImage","当前的ai进度是什么？${aiResponseState}")
    HLog.i("CompositionStudentImage","当前的bitmapState图片进度是什么？${bitmapState}")
    HLog.i("CompositionStudentImage","当前的questionsBitmap图片进度是什么？${questionsBitmap}")
    val verticalScrollState = rememberScrollState()
    Box(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(CORNER_RADIUS))
            .background(color = Color.White),
        contentAlignment = Alignment.TopCenter
    ) {
        when (bitmapState) {
            is CompositionStudentAnswersBitmapState.Loading -> {
                LoadingAnimation()
            }

            is CompositionStudentAnswersBitmapState.Success -> {
                Column(
                    modifier = Modifier
                        .fillMaxWidth()
                        .verticalScroll(state = verticalScrollState) // 使用传入的 ScrollState
                        .then(imageModifier)
                ) {
                    // 学生作答图片（bitmapState）
                    bitmapState.combineImage?.asImageBitmap()?.let { bitmap ->
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clip(RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS))
                                .then(
                                    when (aiResponseState) {
                                        is AiResponseState.ContentOcrFailed,
                                         -> Modifier.border(
                                            1.dp,
                                            Color(0xFFEE0019),
                                            RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS)
                                        )

                                        else -> Modifier
                                    }
                                )
                        ) {
                            Image(
                                modifier = Modifier.fillMaxWidth(),
                                bitmap = bitmap,
                                contentDescription = null,
                                contentScale = ContentScale.FillWidth
                            )
                        }
                    }

                    // 题目图片（questionsBitmap）
                    if (questionsBitmap != null && questionsBitmap is CompositionQuestionImageState.Success) {
                        questionsBitmap.combineImage?.asImageBitmap()?.let { bitmap ->
                            Spacer(modifier = Modifier.height(8.dp))
                            Box(
                                modifier = Modifier
                                    .fillMaxWidth()
                                    .clip(RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS))
                                    .then(
                                        if (aiResponseState is AiResponseState.TitleOcrFailed) {
                                            Modifier.border(
                                                1.dp,
                                                Color(0xFFEE0019),
                                                RoundedCornerShape(STUDENT_IMAGE_CORNER_RADIUS)
                                            )
                                        } else {
                                            Modifier
                                        }
                                    )
                            ) {
                                Image(
                                    modifier = Modifier.fillMaxWidth(),
                                    bitmap = bitmap,
                                    contentDescription = null,
                                    contentScale = ContentScale.FillWidth
                                )
                            }
                        }
                    }
                }

                when (aiResponseState) {
                    is AiResponseState.OcrLoading -> { // 仅OCR 时显示动画
                        ScannerAnimation()
                    }

                    else -> {
                    }
                }
            }
        }
    }
}

@Composable
@Preview(
    widthDp = 800,
)
private fun CompositionStudentImagePreview() {
    val context = LocalContext.current
    CompositionStudentImage(
        aiResponseState = AiResponseState.Loading,
        bitmapState = CompositionStudentAnswersBitmapState.Success(
            BitmapFactory.decodeResource(
                context.resources,
                R.drawable.ic_image
            )
        )
    )
}