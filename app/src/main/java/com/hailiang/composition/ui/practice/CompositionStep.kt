package com.hailiang.composition.ui.practice

import androidx.fragment.app.Fragment
import com.hailiang.xxb.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:14
 */
sealed class CompositionStep(
    val name: String,
    val step: Int,
    val stepIcon: Int,
    val fragment: () -> Fragment?,
) {
    var activatedStep: CompositionStep = this

    data object Default : CompositionStep(
        name = "", step = 0, stepIcon = R.drawable.step_first_answer, fragment = { null })

    data object FirstPractice : CompositionStep(
        name = "首次作答",
        step = 1,
        stepIcon = R.drawable.step_upload_topic,
        fragment = {
            CompositionQuestionTopicFragment()
        },
    ) {
        var editable: Boolean = true
    }

    data object AiGuidance : CompositionStep(
        name = "一稿点拨、改写",
        step = 2,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionAiGuidanceFragment() })

    data object SecondPractice : CompositionStep(
        name = "二次作答",
        step = 2,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionSecondPracticeFragment() })

    data object SubmitPractice : CompositionStep(
        name = "提交",
        step = 4,
        stepIcon = R.drawable.step_second_answer,
        fragment = { null })

    // ----------------------------------------------------------------------
    data object CheckQuestion : CompositionStep(
        name = "上传题目",
        step = 5,
        stepIcon = R.drawable.step_upload_topic,
        fragment = { CompositionQuestionTopicFragment() })

    data object CheckFirstPractice : CompositionStep(
        name = "一稿点拨、改写",
        step = 6,
        stepIcon = R.drawable.step_first_answer,
        fragment = { CompositionCheckFirstFragment() })

    data object CheckSecondPractice : CompositionStep(
        name = "二稿批改",
        step = 7,
        stepIcon = R.drawable.step_second_answer,
        fragment = { CompositionCheckSecondFragment() })
}