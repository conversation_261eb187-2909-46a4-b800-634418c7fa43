package com.hailiang.composition.ui.widget

import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.ui.tooling.preview.Preview
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.CompositionCheckViewModel
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.composition.ui.practice.CompositionViewModel
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState

/**
 * 查看首次作答
 */
@Composable
fun CompositionCheckFirstLayout(
    aiResponseState: AiResponseState,
    studentFeedbackState: EvaluateFeedbackState,
    switchAiTab: (AiSector) -> Unit,
    retry: () -> Unit,
    reload: () -> Unit,
    feedbackClick: (AiSector, EvaluateFeedback, Boolean) -> Unit,
) {
    AiResultLayout(
        aiResponseState = aiResponseState,
        aiTabList = listOf(
            AiSector.Evaluate,
            AiSector.Dibble,
            AiSector.Petrol,
        ),
        switchAiTab = switchAiTab,
        retry = retry,
        reload = reload,
        bottomBar = {
//            CompositionPraiseWidget(
//                aiResponseState = aiResponseState,
//                initFeedback = studentFeedbackState,
//                feedbackClick = feedbackClick,
//            )
        },
        initFeedback = studentFeedbackState,
        feedbackClick = feedbackClick
    )
}

/**
 * 查看二次作答
 */
@Composable
fun CompositionCheckSecondLayout(
    compositionViewModel: CompositionViewModel,
    checkViewModel: CompositionCheckViewModel,
) {
    val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
    val feedbackState by compositionViewModel.studentSecondFeedbackState.collectAsStateWithLifecycle()
    val sentenceClickIndex by compositionViewModel.sentenceClickIndex.collectAsStateWithLifecycle()
    AiResultLayout(
        aiResponseState = aiResponseState,
        aiTabList = listOf(
            AiSector.Evaluate,
            AiSector.Dibble,
        ),
        switchAiTab = {
            compositionViewModel.switchAiTab(
                compositionStep = CompositionStep.CheckSecondPractice,
                aiSector = it
            )
        },
        retry = compositionViewModel::retryAiStream,
        reload = compositionViewModel::reloadAiStream,
        retake = {},
        bottomBar = {
//            CompositionPraiseWidget(
//                aiResponseState = aiResponseState,
//                initFeedback = feedbackState,
//                feedbackClick = { aiSector, evaluateFeedback, selected ->
//                    compositionViewModel.secondDraftFeedback(
//                        aiSector,
//                        evaluateFeedback,
//                        selected
//                    )
//                },
//            )
        },
        sentenceClickIndex = sentenceClickIndex,
        initFeedback = feedbackState,
        feedbackClick = { aiSector, evaluateFeedback, selected ->
            compositionViewModel.secondDraftFeedback(
                aiSector,
                evaluateFeedback,
                selected
            )
        },
    )
}

//@Composable
//@Preview(widthDp = 900, heightDp = 2000)
//private fun CompositionCheckSecondPreview() {
//    CompositionCheckSecondLayout(
//        checkState = CompositionCheckViewModel.TeacherCheckState.Success(
//            teacherChecked = true,
//            selectedAiSector = AiSector.Dibble,
//            aiSubSectorList = CompositionMockData.mockAiSubSectorList(),
//            preScore = 10,
//            score = 15,
//        ),
//        aiTabList = listOf(
//            AiSector.Evaluate,
//            AiSector.Dibble,
//        ),
//        switchAiTab = { },
//        retry = { },
//        reload = {}
//    )
//}

@Composable
@Preview(
    widthDp = 900,
    heightDp = 2000,
)
private fun CompositionCheckFirstPreview() {
    CompositionCheckFirstLayout(
        aiResponseState = AiResponseState.Success(
            allJobStatus = JobStatus.SUCCESS,
            scoreJobStatus = JobStatus.SUCCESS,
            allusionJobStatus = JobStatus.SUCCESS,
            selectedAiSector = AiSector.Dibble,
            aiSubSectorList = CompositionMockData.mockAiSubSectorList(),
            comprehensiveJudge = null,
            adviceList = null,
            allusionList = null,
            score = 18
        ),
        studentFeedbackState = EvaluateFeedbackState(
            feedbackMap = emptyMap(),
            needAnimation = false
        ),
        switchAiTab = { },
        retry = { },
        reload = {},
        feedbackClick = { _, _, _ -> }
    )
}
