package com.hailiang.composition.ui.practice

import android.app.Application
import androidx.lifecycle.viewModelScope
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.core.base.BaseAndroidViewModel
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.view.question.composition.Composition
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import kotlinx.coroutines.flow.MutableStateFlow

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:02
 */
class CompositionCheckViewModel(application: Application) : BaseAndroidViewModel(application) {

    private val compositionRepository = Repository()

//    /**
//     * AI批改信息
//     */
//    @Deprecated("")
//    val teacherCheckState = MutableStateFlow<TeacherCheckState>(TeacherCheckState.Default)

    /**
     * 作文表格比对后的数据
     */
    val compositionTableWithDiffState = MutableStateFlow<Composition?>(null)

    /**
     * 教师点赞状态
     */
    val teacherFeedbackState =
        MutableStateFlow<EvaluateFeedbackState>(
            EvaluateFeedbackState(
                feedbackMap = emptyMap(),
                needAnimation = false
            )
        )

    // ----------------------------------------------------------------------
//    private var curWorkStateId = -1L
//    private var curWorkId = -1L

    /**
     * 综合评价
     */
    private var comprehensiveJudge: ComprehensiveJudge? = null

    /**
     * 点拨
     */
    private var adviceList: List<Advice>? = null

    /**
     * 典故列表（知识加油站）
     */
    private var allusionList: List<Allusion>? = null

    private var aiFirstCheckDetail: CompositionCheckDetail? = null

    private var stepStartTime = -1L
    private var aiStepStartTimeMillis = -1L
    // ----------------------------------------------------------------------

    val aiFirstCheckDetailState = MutableStateFlow<AiResponseState>(AiResponseState.Default)

    //
    fun loadFirstCheckDetail(workId: Long, workStateId: Long) {
        viewModelScope.launchWithException(ThreadPlugins.ioDispatcher()) {
            HLog.i(HTag.TAG, "加载AI首次批改信息: ${workId}_${workStateId}")
            aiFirstCheckDetail = compositionRepository.queryAiFirstCheckDetail(
                workId = workId,
                workStateId = workStateId,
            )
            if(aiFirstCheckDetail?.isDirty() != false) { // 请求远程数据
                compositionRepository.requestAiCheckInfo(
                    workId = workId,
                    workStateId = workStateId,
                    isSubmitted = false
                )
                aiFirstCheckDetail = compositionRepository.queryAiFirstCheckDetail(
                    workId = workId,
                    workStateId = workStateId,
                )
            }
            updateAiFirstCheckState(
                AiResponseState.Success(
                    allJobStatus = JobStatus.SUCCESS,
                    scoreJobStatus = JobStatus.SUCCESS,
                    allusionJobStatus = JobStatus.SUCCESS,
                    selectedAiSector = AiSector.Evaluate,
                    aiSubSectorList = AiSector.Evaluate.getAiSubSectorList(
                        comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                        adviceList = aiFirstCheckDetail?.adviceList,
                        allusionList = aiFirstCheckDetail?.allusionList
                    ),
                    comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                    adviceList = aiFirstCheckDetail?.adviceList,
                    allusionList = aiFirstCheckDetail?.allusionList,
                    score = aiFirstCheckDetail?.score ?: 0,
                )
            )
        }
    }

    fun switchFirstAiTab(compositionStep: CompositionStep, aiSector: AiSector) {
        viewModelScope.launchWithException {
            val aiCorrectState = aiFirstCheckDetailState.value
            if (aiCorrectState is AiResponseState.Success) {
                updateAiFirstCheckState(
                    aiCorrectState.copy(
                        selectedAiSector = aiSector,
                        aiSubSectorList = aiSector.getAiSubSectorList(
                            comprehensiveJudge = aiFirstCheckDetail?.comprehensiveJudge,
                            adviceList = aiFirstCheckDetail?.adviceList,
                            allusionList = aiFirstCheckDetail?.allusionList
                        )
                    )
                )
            }
        }
    }

    private fun updateAiFirstCheckState(aiResponseState: AiResponseState) {
        aiFirstCheckDetailState.value = aiResponseState
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    /**
     * 比对作文
     */
    fun diffCompositionContent(newCompositionTableState: CompositionTableState.Success) {
        if(!newCompositionTableState.needDoCompare) return
        HLog.i(HTag.TAG, "开始比对文本变化~")
        viewModelScope.launchWithException(ThreadPlugins.ioDispatcher()) {
            val composition = newCompositionTableState.composition
            composition?.resetCompare()
            newCompositionTableState.ocrTitle?.let {
                composition?.setCompareTitle(title = it)
            }
            newCompositionTableState.ocrContent?.jsonToList(String::class.java)?.forEach {
                composition?.appendCompareParagraph(it)
            }
            composition?.doCompare()
            composition?.let {
                compositionTableWithDiffState.value = it
            }
        }
    }


    // ----------------------------------------------------------------------
    override fun onCleared() {
        super.onCleared()
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    sealed class TeacherCheckState {
        object Default : TeacherCheckState()

        object Failed : TeacherCheckState()

        object Reload : TeacherCheckState()

        data class Success(
            @Deprecated("")
            val teacherChecked: Boolean = true,
            val selectedAiSector: AiSector = AiSector.Evaluate,
            val aiSubSectorList: List<AiSubSector> = emptyList(),
            /**
             * 综合评价
             */
            val comprehensiveJudge: ComprehensiveJudge? = null,
            /**
             * 点拨
             */
            val adviceList: List<Advice>? = null,
            val score: Int = -1,
            val preScore: Int = -1,
        ) : TeacherCheckState()

        object Loading : TeacherCheckState()
    }
}