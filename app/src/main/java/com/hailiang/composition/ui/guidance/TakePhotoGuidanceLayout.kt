package com.hailiang.composition.ui.guidance

import androidx.compose.foundation.ExperimentalFoundationApi
import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.ColumnScope
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.pager.HorizontalPager
import androidx.compose.foundation.pager.rememberPagerState
import androidx.compose.foundation.rememberScrollState
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.verticalScroll
import androidx.compose.material3.Text
import androidx.compose.material3.TextButton
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.text.TextStyle
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hailiang.xxb.composition.R


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun AiGuidanceLayout(onOkClick: () -> Unit) {
    AiGuidanceGroup {
        TopHeading()
        Spacer(Modifier.height(14.dp))
        StandardExampleLayout()
        AcceptButton(onOkClick)
        CommonMistakesLayout()
    }
}

@Composable
fun AiGuidanceGroup(content: @Composable ColumnScope.() -> Unit) {
    Box(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colors = listOf(Color(0xFFDFE9FF), Color(0xFFE3F3FF)),
                    start = Offset(x = 0f, y = 0f),
                    end = Offset(x = Float.POSITIVE_INFINITY, y = 0f)
                )
            ),
        contentAlignment = Alignment.TopCenter
    ) {
        Column(
            modifier = Modifier
                .verticalScroll(rememberScrollState())
                .padding(0.dp, 28.dp),
            horizontalAlignment = Alignment.CenterHorizontally,
            verticalArrangement = Arrangement.Center,
            content = content
        )
    }
}

@Composable
fun TopHeading() {
    Row(
        verticalAlignment = Alignment.CenterVertically,
        horizontalArrangement = Arrangement.spacedBy(8.dp)
    ) {
        Image(
            painter = painterResource(R.drawable.icon_explain),
            contentDescription = null,
        )
        Text(
            text = "正对纸张，卷面平整清晰、请按照阅读顺序拍摄(请勿把多栏作文纸拍在一张图上)",
            style = TextStyle(
                fontSize = 16.sp,
                color = Color(0xff515a6e)
            )
        )
    }
}


@OptIn(ExperimentalFoundationApi::class)
@Composable
fun StandardExampleLayout() {
    val imageResources = listOf(
        R.drawable.guidance1,
        R.drawable.guidance2,
        R.drawable.guidance3
    )
    val totalPage = imageResources.size
    val pagerState = rememberPagerState { totalPage }
    HorizontalPager(
        state = pagerState,
        modifier = Modifier.fillMaxSize()
    ) { page ->
        Box(modifier = Modifier.height(500.dp)) {
            Image(
                painter = painterResource(id = imageResources[page]),
                contentDescription = "示例图片$page",
                modifier = Modifier.fillMaxWidth(),
                contentScale = ContentScale.Inside
            )
        }
    }
    Row(
        Modifier
            .wrapContentHeight()
            .fillMaxWidth()
            .padding(bottom = 8.dp),
        horizontalArrangement = Arrangement.Center
    ) {
        repeat(totalPage) { iteration ->
            val color = if (pagerState.currentPage == iteration) {
                Color(0xFF5565FF)
            } else {
                Color(0xFFB7C4FF)
            }
            Box(
                modifier = Modifier
                    .padding(horizontal = 10.dp, vertical = 5.dp)
                    .clip(CircleShape)
                    .background(color)
                    .size(16.dp)
            )
        }
    }

}

@Composable
fun AcceptButton(onClick: () -> Unit) {
    TextButton(
        onClick = onClick,
        modifier = Modifier
            .width(300.dp)
            .height(56.dp)
            .clip(shape = RoundedCornerShape(28.dp))
            .background(
                brush = Brush.linearGradient(
                    0.0f to Color(0xFFBF9DF5),
                    1.0f to Color(0xFF3968FF),
                )
            )
    ) {
        Text(
            text = "知道了",
            color = Color.White,
            fontSize = 18.sp,
            style = TextStyle(fontWeight = FontWeight.Bold)
        )
    }
}

@Composable
fun CommonMistakesLayout() {
    Image(
        painter = painterResource(id = R.drawable.icon_guidance_problem),
        contentDescription = "11",
        modifier = Modifier
            .wrapContentSize()
            .clip(RoundedCornerShape(8.dp))
            .padding(vertical = 20.dp),
    )
    Row(
        modifier = Modifier.fillMaxWidth(),
        horizontalArrangement = Arrangement.Center
    ) {
        Image(
            modifier = Modifier.size(344.dp, 472.dp),
            painter = painterResource(id = R.drawable.guidance_problem1),
            contentScale = ContentScale.FillBounds,
            contentDescription = null,
        )
        Spacer(modifier = Modifier.width(30.dp))
        Image(
            modifier = Modifier.size(344.dp, 472.dp),
            painter = painterResource(id = R.drawable.guidance_problem2),
            contentDescription = null,
            contentScale = ContentScale.FillBounds
        )
        Spacer(modifier = Modifier.width(30.dp))
        Image(
            modifier = Modifier.size(344.dp, 472.dp),
            painter = painterResource(id = R.drawable.guidance_problem3),
            contentDescription = null,
            contentScale = ContentScale.FillBounds
        )
    }
}


@Preview(
    device = "spec:width=1280dp,height=800dp,orientation=landscape",
    showBackground = true
)
@Composable
fun PreviewPage() {
    AiGuidanceLayout {}
}

