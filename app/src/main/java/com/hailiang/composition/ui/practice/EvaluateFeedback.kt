package com.hailiang.composition.ui.practice

import androidx.annotation.DrawableRes
import com.hailiang.composition.data.bean.request.EvaluateType
import com.hailiang.xxb.composition.R


/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/19 15:31
 */
sealed class EvaluateFeedback(
    @DrawableRes val normalRes: Int,
    @DrawableRes val selectedRes: Int,
    val evaluateType: EvaluateType,
) {
    object Like : EvaluateFeedback(
        normalRes = R.drawable.ic_praise_normal,
        selectedRes = R.drawable.ic_praise_selected,
        evaluateType = EvaluateType.Like,
    )

    object Dislike : EvaluateFeedback(
        normalRes = R.drawable.ic_trea_normal,
        selectedRes = R.drawable.ic_trea_selected,
        evaluateType = EvaluateType.Dislike,
    )
}