package com.hailiang.composition.ui.widget

import android.graphics.BlurMaskFilter
import android.graphics.Paint
import androidx.compose.animation.core.animateFloatAsState
import androidx.compose.animation.core.tween
import androidx.compose.foundation.Canvas
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.CircleShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.StrokeCap
import androidx.compose.ui.graphics.drawscope.Stroke
import androidx.compose.ui.graphics.drawscope.drawIntoCanvas
import androidx.compose.ui.graphics.nativeCanvas
import androidx.compose.ui.graphics.toArgb
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import kotlin.math.cos
import kotlin.math.sin

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/7 17:50
 */
class ProgressWidget {
}


@Composable
fun CircularProgressBar(
    progress: Int,
    animDuration: Int = 0,
    animDelay: Int = 0,
) {
    var animationPlayed by remember { mutableStateOf(false) }
    val strokeWidth = 10.dp
    val indicatorSize = 8.dp
    val radius = (150.dp - strokeWidth) / 2F
    //
//    val curPercentage = remember { mutableStateOf(progress / 100F) }
    val curPercentage = animateFloatAsState(
        targetValue = if (animationPlayed) (progress / 100F) else 0f,
        animationSpec = tween(
            durationMillis = animDuration,
            delayMillis = animDelay
        )
    )

    LaunchedEffect(key1 = true) {
        animationPlayed = true
    }

    val startColor = Color(0xFF466DFF)
    val endColor = Color(0xFFAC77FF)
    val centerColor = Color(0xFF7A75FF)
    Box(
        contentAlignment = Alignment.Center,
        modifier = Modifier
            .size(168.dp)
            .clip(CircleShape)
            .background(Color.White)
    ) {
        Canvas(modifier = Modifier.size(radius * 2f)) {
            val strokeWidthPx = strokeWidth.toPx()
            // 创建优化后的渐变画笔
            val brush = Brush.sweepGradient(
                colorStops = arrayOf(
                    0.0f to centerColor,
                    0.1f to endColor,
                    0.4f to endColor,
                    0.8f to startColor,
                    1.0f to centerColor
                ),
                center = center
            )

            // 绘制背景圆环
            drawArc(
                color = endColor.copy(alpha = 0.1f),
                startAngle = 0f,
                sweepAngle = 360f,
                useCenter = false,
                style = Stroke(width = strokeWidthPx, cap = StrokeCap.Round)
            )
            // 绘制进度圆弧
            drawArc(
                brush = brush,
                startAngle = -90F,
                sweepAngle = 360 * curPercentage.value,
                useCenter = false,
                style = Stroke(width = strokeWidthPx, cap = StrokeCap.Round)
            )
            // 添加圆形指示器
            val angle = (360 * curPercentage.value - 90) * (Math.PI / 180f).toFloat()
            val indicatorX = center.x + cos(angle) * size.width / 2
            val indicatorY = center.y + sin(angle) * size.width / 2

//            drawCircle(
//                color = color,
//                radius = strokeWidthPx / 2,
//                center = Offset(indicatorX, indicatorY)
//            )
//            drawCircle(
//                color = Color(0x4D9141FF),
//                radius = (indicatorSize + 2.dp).toPx(),
//                center = Offset(indicatorX, indicatorY),
//                blendMode = BlendMode.SrcOver
//            )

            // 绘制模糊阴影
            drawIntoCanvas { canvas ->
                canvas.nativeCanvas.drawCircle(
                    indicatorX,
                    indicatorY,
                    (indicatorSize + 1.dp).toPx(),
                    Paint().apply {
                        color = Color(0x4D9141FF).toArgb()
                        alpha = 50
                        maskFilter = BlurMaskFilter(
                            8f,
                            BlurMaskFilter.Blur.NORMAL
                        )
                    }
                )
            }


            // 添加内部白色圆点
            drawCircle(
                color = Color.White,
                radius = indicatorSize.toPx(),
                center = Offset(indicatorX, indicatorY)
            )
        }
        // 显示百分比文字
        Text(
            text = "${(curPercentage.value * 100).toInt()}%",
            fontSize = 38.sp,
            fontWeight = FontWeight.Bold,
            color = Color.Black
        )
    }
}

@Preview
@Composable
fun CircularProgressBarPreview() {
    CircularProgressBar(progress = 20)
}