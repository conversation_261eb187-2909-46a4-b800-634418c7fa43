package com.hailiang.composition.ui.widget

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.widget.FrameLayout
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.viewinterop.AndroidView
import androidx.core.content.res.ResourcesCompat
import com.hailiang.hlutil.DisplayUtil
import com.hailiang.ui.designsystem.util.TypeFaceHelper
import com.hailiang.view.question.composition.Composition
import com.hailiang.view.question.composition.CompositionIndex
import com.hailiang.view.question.composition.CompositionTableLayout
import com.hailiang.view.question.composition.listener.SentenceClickListener
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.LayoutCompositionTableBinding
import com.robertlevonyan.demo.camerax.utils.bottomMargin
import com.hailiang.hlutil.HLog

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/31 10:16
 */
class CompositionTableContainer : FrameLayout {
    constructor(context: Context) : super(context)
    constructor(context: Context, attrs: AttributeSet?) : super(context, attrs)
    constructor(context: Context, attrs: AttributeSet?, defStyleAttr: Int) : super(
        context,
        attrs,
        defStyleAttr
    )

    constructor(
        context: Context,
        attrs: AttributeSet?,
        defStyleAttr: Int,
        defStyleRes: Int,
    ) : super(context, attrs, defStyleAttr, defStyleRes)

    val binding = LayoutCompositionTableBinding.inflate(LayoutInflater.from(context), this, true)
    private var preComposition: Composition? = null

    init {
        ResourcesCompat.getFont(context, R.font.table)?.let { font ->
            binding.compositionTableLayout.setFontType(font)
        }
    }

    fun setComposition(composition: Composition?) {
        if (preComposition != composition) {
            preComposition = composition
            binding.compositionTableLayout.setComposition(composition)
        }
    }

    fun setEditable(editable: Boolean) {
        binding.compositionTableLayout.editable = editable
    }

    fun setTextWatcher(textWatcher: CompositionTableLayout.TextWatcher?) {
        binding.compositionTableLayout.textWatcher = textWatcher
    }

    fun setSentenceClickListener(sentenceListener: SentenceClickListener?) {
        HLog.i("CompositionTableContainer", "setSentenceClickListener")
        binding.compositionTableLayout.sentenceClickListener = sentenceListener
    }

    fun setScore(score: Int) {
        if (score >= 0) {
            ResourcesCompat.getFont(context, R.font.table)?.let {
                TypeFaceHelper.setTypeface(binding.compositionScoreTv, it)
            }
            binding.compositionScoreTv.text = "$score"
            binding.compositionScoreTv.visibility = VISIBLE
        } else {
            binding.compositionScoreTv.visibility = GONE
        }
    }

    fun showBottomPadding(showBottomPadding: Boolean) {
        if (showBottomPadding) {
            binding.compositionTableLayout.bottomMargin = DisplayUtil.pxFromDp(100F).toInt()
        } else {
            binding.compositionTableLayout.bottomMargin = 0

        }
    }
}

@Composable
fun CompositionTableWidget(
    composition: Composition?,
    editable: Boolean = false,
    score: Int = -1,
    showBottomPadding: Boolean = false,
    textWatcher: CompositionTableLayout.TextWatcher? = null,
    sentenceListener: SentenceClickListener? = null
) {
    var loadingAnim by remember { mutableStateOf(true) }
    Box(
        modifier = Modifier.fillMaxSize(),
        contentAlignment = Alignment.TopCenter
    ) {
        when {
            composition != null -> {
                AndroidView(
                    modifier = Modifier.fillMaxWidth(),
                    factory = { context ->
//                        HLog.i(HTag.TAG, "factory CompositionTableContainer")
                        CompositionTableContainer(context).apply {
                            setTextWatcher(object : CompositionTableLayout.TextWatcher {
                                override fun onTextChanged(
                                    title: String?,
                                    content: String?,
                                    returnRangeList: List<CompositionIndex.ReturnRange>,
                                ) {
                                    textWatcher?.onTextChanged(title, content, returnRangeList)
                                }

                                override fun onTextInitFinish(
                                    title: String?,
                                    content: String?,
                                    returnRangeList: List<CompositionIndex.ReturnRange>,
                                ) {
                                    textWatcher?.onTextInitFinish(
                                        title,
                                        content,
                                        returnRangeList
                                    )
                                    loadingAnim = false
                                }
                            })
                            setSentenceClickListener(sentenceListener)
                        }
                    },
                    update = { view ->
//                        HLog.i(HTag.TAG, "update CompositionTableContainer")
                        view.setEditable(editable)
                        view.showBottomPadding(showBottomPadding)
                        view.setComposition(composition)
                        view.setScore(score)
                    }
                )
                if (loadingAnim) {
                    LoadingAnimation()
                }
            }

            else -> {
                LoadingAnimation()
            }
        }

    }
}