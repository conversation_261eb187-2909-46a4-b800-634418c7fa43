package com.hailiang.composition.ui.practice

import androidx.compose.foundation.layout.aspectRatio
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.viewinterop.AndroidView
import org.libpag.PAGFile
import org.libpag.PAGImageView

@Composable
fun PagAnimation(
    modifier: Modifier = Modifier,
    pagFilePath: String,
    loopCount: Int = 0
) {
    val context = LocalContext.current

    // 加载 PAG 文件
    val pagFile = remember {
        PAGFile.Load(context.assets, pagFilePath)
    }

    val aspectRatio = pagFile.width().toFloat() / pagFile.height().toFloat()

    val layoutModifier = modifier
        .fillMaxWidth()
        .aspectRatio(aspectRatio) // 强制宽高比

    val pagView = remember {
        PAGImageView(context).apply {
            composition = pagFile
            setRepeatCount(loopCount)
            play()
        }
    }

    AndroidView(
        factory = { pagView },
        modifier = layoutModifier
    )
}
