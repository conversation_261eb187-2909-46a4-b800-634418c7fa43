package com.hailiang.composition.ui

import android.os.Bundle
import android.widget.Button
import android.widget.EditText
import android.widget.ImageView
import android.widget.TextView
import androidx.activity.enableEdgeToEdge
import androidx.appcompat.app.AppCompatActivity
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import com.hailiang.common.download.resource.WorkResource
import com.hailiang.common.download.resource.WorkResourceDownload
import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.compress.ZipUtil
import com.hailiang.hlutil.id
import com.hailiang.question.consts.MaterialConst
import com.hailiang.textcorrection.dl.DownloadUtil
import com.hailiang.textcorrection.dl.DownloadUtil.DownloadCallback
import com.hailiang.textcorrection.dl.TextCorrectionManager
import com.hailiang.workcloud.download.resource.ResourceDownloadCallback
import com.hailiang.xxb.composition.R
import java.io.File
import java.io.FileOutputStream
import kotlin.getValue

class TestActivity : AppCompatActivity() {

    private val editTextText: EditText by id(R.id.editTextText)
    private val button: Button by id(R.id.button)


    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        setContentView(R.layout.activity_test)
        //初始化
        TextCorrectionManager.initialize(
            context = this,
            url = "https://jzjx-prod-resource.hailiangedu.com/others/2025/05/25/da2634ddc9c34eaa9c231029d0cbdd86.zip",
            callbackState = {
                HLog.i(HTag.TAG, "TextCorrectionManager 初始化状态 : $it")
            },
            retry = true
        )
        editTextText.setText("这一次，灾难化成了一股波涛汹涌的海水，卷走了爸爸，卷走了我的靠山，卷走了我的勇气……现在，我只剩下无穷无尽的害怕，害怕我最后的唯一也会被带走，我该怎么办。")
        button.setSingleClickListener {
            val correctText = TextCorrectionManager.correctText(editTextText.text.toString().trim())
            when (correctText) {
                is TextCorrectionManager.CorrectionResult.Success -> {
                    HLog.i(HTag.TAG, "文本纠错结果 : ${correctText.message}")
                }

                is TextCorrectionManager.CorrectionResult.Error -> {
                    HLog.i(HTag.TAG, "文本纠错结果 : ${correctText.message}")
                }
            }
        }
    }
}