package com.hailiang.composition.ui.practice

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.activity.result.contract.ActivityResultContracts
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.photograph.PhotoViewModel
import com.hailiang.composition.ui.photograph.TakePhotoCompositionActivity
import com.hailiang.composition.ui.widget.AiGuideLayout
import com.hailiang.composition.ui.widget.CompositionStudentImage
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.ext.visible
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.FragmentCompositionAiJudgeBinding


/**
 * Description: AI 点拨
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionAiGuidanceFragment : BaseFragment(R.layout.fragment_composition_ai_judge) {

    private var _binding: FragmentCompositionAiJudgeBinding? = null
    private val binding get() = _binding!!

    private val compositionViewModel by activityViewModels<CompositionViewModel>()
    private val takePhotoLauncher =
        registerForActivityResult(ActivityResultContracts.StartActivityForResult()) { result ->
            HLog.i(HTag.TAG, "takePhotoLauncher result = $result")
            requireActivity().finish()
        }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionAiJudgeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.compositionAiContentContainer.clipOutline(20.dp)
        binding.compositionStudentImagesContainer.let {
            it.removeAllViews()
            it.addView(
                ComposeView(requireActivity()).apply {
                    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                    setContent {
                        val studentAnswerBitmap by compositionViewModel.studentAnswerBitmapState.collectAsStateWithLifecycle()
                        val compositionQuestionImagesBitmap by compositionViewModel.compositionQuestionImagesState.collectAsStateWithLifecycle()
                        val aiCorrectState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                        CompositionStudentImage(
                            aiResponseState = aiCorrectState,
                            bitmapState = studentAnswerBitmap,
                            questionsBitmap = compositionQuestionImagesBitmap
                        )
                    }
                }
            )
        }
        // ----------------------------------------------------------------------
        // 加载学生图片
        compositionViewModel.studentAnswerImages.launchAndCollect(viewLifecycleOwner) {
            compositionViewModel.downloadAndCombineStudentAnswers(it.imageResources)
        }
        compositionViewModel.aiResponseState.launchAndCollect(viewLifecycleOwner) { status ->
            when (status) {
                is AiResponseState.Success -> { // 成功就显示按钮
                    binding.compositionSecondPracticeBtn.visible(status.allJobStatus.isSuccess())
                }

                else -> {
                    binding.compositionSecondPracticeBtn.visible(false)
                }
            }
        }
        // ----------------------------------------------------------------------
        binding.compositionSecondPracticeBtn.setSingleClickListener {
//            compositionViewModel.reportAiTabSwitchEvent(practiceViewModel.practiceStepState.value)
            compositionViewModel.toSecondPractice()
        }
        binding.compositionAiContentContainer.removeAllViews()
        binding.compositionAiContentContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    AiGuideLayout(
                        compositionViewModel = compositionViewModel,
                        retake = ::takePhotos
                    )
                }
            }
        )
    }

    private fun takePhotos() {
        val aiResponseState = compositionViewModel.aiResponseState.value
        var clearType = PhotoViewModel.TYPE_COMPOSITION
        
        // 根据AI响应状态决定清理类型
        if (aiResponseState is AiResponseState.TitleOcrFailed) {
            clearType = when {
                aiResponseState.compositionStatusResponse?.ocrResult?.getOcrJobStatus()?.isFailed() == true -> {
                    PhotoViewModel.TYPE_ALL // 清理所有
                }
                else -> {
                    PhotoViewModel.TYPE_TITLE // 仅清理题目
                }
            }
        }
        
        takePhotoLauncher.launch(
            TakePhotoCompositionActivity.createIntent(
                requireActivity(),
                workId = compositionViewModel.curWorkId,
                workStateId = compositionViewModel.curWorkStateId,
                clearType
            )
        )
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        compositionViewModel.stopLoopAiResult()
        _binding = null
    }
}