package com.hailiang.composition.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.gestures.detectDragGestures
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.wrapContentHeight
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.input.pointer.pointerInput
import androidx.compose.ui.layout.onSizeChanged
import androidx.compose.ui.platform.LocalDensity
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.IntSize
import androidx.compose.ui.unit.dp
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/22 10:27
 */
@Composable
fun SplitScreenLayout(
    defaultTopHeight: Float,
    dragHeight: Dp = 56.dp,
    /**
     * 上方视图的最小高度
     */
    minTopHeight: Dp = 120.dp,
    /**
     * 底部视图的最小高度
     */
    minBottomHeight: Dp = 80.dp,
    heightChanged: (top: Float) -> Unit,
    topLayout: @Composable () -> Unit,
    bottomLayout: @Composable () -> Unit,
    dragLayout: @Composable () -> Unit,
) {
    // 上方视图的高度，默认300dp
    var topViewHeight by remember { mutableStateOf(defaultTopHeight.dp) }
    // 实际可用高度
    var availableHeight by remember { mutableStateOf(0.dp) }
    val density = LocalDensity.current
//    LaunchedEffect(availableHeight) {
//        if (defaultTopHeight == 0F) {
//            topViewHeight = with(density) {
//                (availableHeight - minBottomHeight - dragHeight).coerceAtLeast(minTopHeight)
//            }
//        }
//    }
    Column(
        modifier = Modifier
            .fillMaxSize()
            .clip(shape = RoundedCornerShape(CORNER_RADIUS))
            .background(Color.White)
            .onSizeChanged { size ->
                // 监听实际可用高度
                with(density) {
                    availableHeight = size.height.toDp()
                }
            }
    ) {
        // 上方视图
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(topViewHeight)
        ) {
            topLayout()
        }
        // 中间可拖拽视图
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .height(dragHeight)
                .background(Color.Transparent)
                .pointerInput(Unit) {
                    detectDragGestures { _, dragAmount ->
                        // 计算新的上方高度
                        val newTopHeight = topViewHeight + dragAmount.y.toDp()
                        // 计算新的底部高度
                        val newBottomHeight =
                            availableHeight - newTopHeight - dragHeight
                        // 限制上方和底部高度范围
                        if (newBottomHeight >= minBottomHeight && newTopHeight >= minTopHeight) {
                            topViewHeight = newTopHeight
                            heightChanged(topViewHeight.value)
                        }
                    }
                },
            contentAlignment = Alignment.BottomCenter
        ) {
            dragLayout()
        }
        // 最下方视图（撑满剩余空间）
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1f) // 撑满剩余空间
        ) {
            bottomLayout()
        }
    }
}

@Composable
fun ResponsiveLayout(
    topLayout: @Composable () -> Unit,
    bottomLayout: @Composable () -> Unit,
    onTopSizeChanged: (IntSize) -> Unit = {}
) {
    Column {
        // 上方View
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .weight(1F)
                .onSizeChanged {
                    onTopSizeChanged(it)
                    HLog.i(HTag.TAG, "ResponsiveLayout: ${it.width} ${it.height}")
                }
        ) {
            topLayout()
        }
        // 下方View
        Box(
            modifier = Modifier
                .fillMaxWidth()
                .wrapContentHeight()
        ) {
            bottomLayout()
        }
    }
}