package com.hailiang.composition.ui.practice

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.widget.CompositionCheckSecondLayout
import com.hailiang.composition.ui.widget.CompositionTableWidget
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.view.question.composition.CompositionIndex
import com.hailiang.view.question.composition.CompositionTableLayout
import com.hailiang.view.question.composition.bean.CompositionSentenceInfo
import com.hailiang.view.question.composition.listener.SentenceClickListener
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.FragmentCompositionCheckSecondBinding
import kotlinx.coroutines.launch

/**
 * Description: 查看二次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionCheckSecondFragment : BaseFragment(R.layout.fragment_composition_check_second) {

    private var _binding: FragmentCompositionCheckSecondBinding? = null
    private val binding get() = _binding!!

    private val checkViewModel by activityViewModels<CompositionCheckViewModel>()
    private val compositionViewModel by activityViewModels<CompositionViewModel>()
    private val saList = mutableListOf<CompositionSentenceInfo>()
    private val sentenceClickListener = object : SentenceClickListener {
        override fun onSentenceClick(sentenceInfo: CompositionSentenceInfo) {
            compositionViewModel.sentenceClickIndex.value = sentenceInfo.index - 1
        }
    }
    private var compositionText = ""

    private val compositionWatcher = object : CompositionTableLayout.TextWatcher {
        override fun onTextChanged(
            title: String?,
            content: String?,
            returnRangeList: List<CompositionIndex.ReturnRange>,
        ) {
        }

        override fun onTextInitFinish(
            title: String?,
            content: String?,
            returnRangeList: List<CompositionIndex.ReturnRange>,
        ) {
            compositionText = content ?: ""
            updateGoodBadSentences()
        }
    }


    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionCheckSecondBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.compositionStudentContentContainer.clipOutline(15.dp)
        binding.compositionTeacherCheckContainer.clipOutline(15.dp)
        binding.compositionStudentContentContainer.let {
            it.removeAllViews()
            it.addView(ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val composition by checkViewModel.compositionTableWithDiffState.collectAsStateWithLifecycle()
//                    val scoreState by checkViewModel.teacherCheckState.collectAsStateWithLifecycle()
                    val scoreState by compositionViewModel.scoreState.collectAsStateWithLifecycle()
                    var score by remember { mutableStateOf(-1) }
                    LaunchedEffect(scoreState) {
                        scoreState.let { status ->
                            score = scoreState
                        }
                    }

                    CompositionTableWidget(
                        composition = composition,
                        score = score,
                        editable = false,
                        textWatcher = compositionWatcher,
                        sentenceListener = sentenceClickListener
                    )
                }
            })
        }

        // ----------------------------------------------------------------------
        binding.compositionTeacherCheckContainer.removeAllViews()
        binding.compositionTeacherCheckContainer.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    CompositionCheckSecondLayout(
                        compositionViewModel = compositionViewModel,
                        checkViewModel = checkViewModel,
                    )
                }
            }
        )
        lifecycleScope.launch {
            compositionViewModel.observeAiSuccessOnly().collect {
                if (saList.isEmpty()) {
                    updateGoodBadSentences()
                }
            }
        }
    }

    fun updateGoodBadSentences() {
        if (compositionText.isEmpty()) {
            return
        }
        if (_binding == null) {
            return
        }
        saList.clear()
        compositionViewModel.aiResponseState.value.let { aiResponseState ->
            if (aiResponseState is AiResponseState.Success) {
                aiResponseState.adviceList?.firstOrNull { it.type == "sentence" }?.let {
                    it.judgeList?.forEachIndexed { index, judge ->
                        judge.content?.trim()?.let { text ->
                            val result = if (text.endsWith("。")) {
                                text.removeSuffix("。")
                            } else {
                                text
                            }
                            findContentRange(result, compositionText)?.let { (start, end) ->
                                saList.add(
                                    CompositionSentenceInfo(
                                        start = start,
                                        end = end,
                                        index = index + 1,
                                        isGood = judge.advantage.isNullOrEmpty() == false,
                                        text = judge.advantage ?: judge.suggestion ?: "",
                                    )
                                )
                            }
                        }
                    }
                }
            }
            val compositionTableLayout =
                binding.compositionStudentContentContainer.findViewById<CompositionTableLayout>(R.id.composition_table_layout)
            compositionTableLayout.updateSentenceInfo(saList)
        }
    }


    fun findContentRange(judge: String?, totalContent: String): Pair<Int, Int>? {
        val content = judge?.let {
            it.substringAfter("：")
                .replace(Regex("""\*\*第\d+句\*\*"""), "") // 移除句子序号
                .replace("*", "")
                .replace("-", "")
                .trim()
        } ?: return null

        if (!content.contains("……")) {
            // 不包含省略号，直接查找完整内容
            val startIndex = totalContent.indexOf(content)
            return if (startIndex != -1) {
                Pair(startIndex, startIndex + content.length)
            } else null
        } else {
            val parts = content.split("……")
            val firstPart = parts.first().trim()
            val lastPart = parts.last().trim()

            val startIndex = totalContent.indexOf(firstPart)
            if (startIndex == -1) return null

            val endPartIndex = totalContent.indexOf(lastPart, startIndex + firstPart.length)
            return if (endPartIndex != -1) {
                Pair(startIndex, endPartIndex + lastPart.length)
            } else null
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        compositionViewModel.stopLoopAiResult()
        _binding = null
    }
}