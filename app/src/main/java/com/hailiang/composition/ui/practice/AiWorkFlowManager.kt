package com.hailiang.composition.ui.practice

import com.hailiang.composition.data.AiStreamClient
import com.hailiang.composition.data.Repository
import com.hailiang.composition.data.bean.AiStreamChunkResponse
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.AiStreamStatus
import com.hailiang.composition.data.bean.AiStreamType
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.composition.data.repository.WorkReposition
import com.hailiang.composition.ui.practice.AiWorkFlowManager.AiFlow.Streaming
import com.hailiang.core.ext.launchOnHttp
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.JsonUtil
import com.hailiang.hlutil.date.DateUtil
import com.hailiang.workcloud.data.vo.CompositionCheckDetail
import kotlinx.coroutines.Job
import kotlinx.coroutines.coroutineScope
import kotlinx.coroutines.delay
import kotlinx.coroutines.flow.Flow
import kotlinx.coroutines.flow.MutableSharedFlow

/**
 * Description: AI流管理
 * 若 Finish 保证 OCR、综合评价、点拨 都完成了，加油站和分数可能还会完成
 * <AUTHOR>
 * @version 2025/4/8 16:58
 */
class AiWorkFlowManager {

    private val repository = Repository()
    private val workReposition = WorkReposition()
    private val aiStreamClient by lazy {
        AiStreamClient(
            dataParser = {
                try {
                    JsonUtil.parseJson(it, AiStreamChunkResponse::class.java)
                } catch (e: OutOfMemoryError) {
                    HLog.e(HTag.TAG, "JSON解析内存不足: ${e.message}")
                    null
                } catch (e: Exception) {
                    HLog.e(HTag.TAG, "JSON解析错误: ${e.message}")
                    null
                }
            },
        )
    }
    val aiFlowState = MutableSharedFlow<AiFlow>(replay = 1)
    private var cachedStreamDetail: AiStreamDetail? = null
    private var cachedStreamType: AiStreamType = AiStreamType.Default
    private var number = 0
    private var isFirst: Boolean = true
    // ----------------------------------------------------------------------
    /**
     * 请求首次作答AI批改状态
     */
    suspend fun requestAiStreamStatus(workId: Long, workStateId: Long) {
//        mock()
//        return
        if (isFirst) {
            updateFlowState(AiFlow.Default)
            isFirst = false
        }
        val result = repository.checkAiStreamStatus(
            workId = workId,
            workStateId = workStateId
        )
        //
        val streamStatusResponse = result.data
        if (!result.isSuccess || streamStatusResponse == null) {
            requestError()
            return
        }
        val titleOcrResult = streamStatusResponse.titleOcrResult
        val titleOcrJob = streamStatusResponse.getTitleOcrStatus()
        if (titleOcrJob.isFailed()) {
            HLog.d(HTag.TAG, "题目ocr识别失败: $titleOcrResult")
            updateFlowState(
                AiFlow.TitleOrcFailed( compositionStatusResponse = streamStatusResponse, errorMessage = titleOcrResult?.message ?: "")
            )
            return
        }
        if (titleOcrJob.isRunning()) {
            HLog.d(HTag.TAG, "题目ocr识别中: $titleOcrResult")
            updateFlowState(
                AiFlow.TitleOcrPreparing(
                    ocrCreateTimeMillis = DateUtil.stringToDate(
                        dateStr = titleOcrResult?.createTime ?: "",
                        pattern = DateUtil.DEFAULT_PATTERN
                    )?.time ?: System.currentTimeMillis()
                )
            )
            return
        }
        // 题目ocr识别成功
        val orcJob = streamStatusResponse.getOcrJobStatus()
        val ocrResult = streamStatusResponse.ocrResult
        if (orcJob.isFailed()) {
            HLog.d(HTag.TAG, "作文ocr识别失败: $ocrResult")
            updateFlowState(AiFlow.OcrFailed(errorMessage = ocrResult?.message ?: ""))
            return
        }
        if (orcJob.isRunning()) {
            HLog.d(HTag.TAG, "作文ocr识别中: $ocrResult")
            updateFlowState(
                AiFlow.ContentOcrPreparing(
                    ocrCreateTimeMillis = DateUtil.stringToDate(
                        dateStr = ocrResult?.createTime ?: "",
                        pattern = DateUtil.DEFAULT_PATTERN
                    )?.time
                        ?: System.currentTimeMillis()
                )
            )
            return
        }
        // ----------------------------------------------------------------------
        val failedType = streamStatusResponse.getFailedType()
        if (failedType != AiStreamType.Default) {
            updateFlowState(AiFlow.StreamError(failedType))
            retryAiStream(workId = workId, workStateId = workStateId, type = failedType)
            return
        }
        // ----------------------------------------------------------------------
        // 流执行状态
        val runningType = streamStatusResponse.getRunningType()
        if (runningType != AiStreamType.Default) {
            updateFlowState(aiFlow = AiFlow.StreamPreparing(streamType = runningType))
            requestAiStreamInfoSuspend(
                workId = workId,
                workStateId = workStateId,
                aiStreamStatus = AiStreamStatus.Running,
                aiStreamType = runningType
            )
            return
        }
        // ----------------------------------------------------------------------
        // 流准备状态
        val readyType = streamStatusResponse.getReadyType()
        if (readyType != AiStreamType.Default) {
            updateFlowState(aiFlow = AiFlow.StreamPreparing(streamType = readyType))
            requestAiStreamInfoSuspend(
                workId = workId,
                workStateId = workStateId,
                aiStreamStatus = AiStreamStatus.Ready,
                aiStreamType = readyType
            )
            return
        }
        // ----------------------------------------------------------------------
        val preparingType = streamStatusResponse.getPreparingType()
        if (preparingType != AiStreamType.Default) {
            updateFlowState(aiFlow = AiFlow.StreamPreparing(streamType = preparingType))
            return
        }
        // ----------------------------------------------------------------------
        val successType = streamStatusResponse.getFinishType()
        updateStreamType(successType)
        if (successType != AiStreamType.Default) {
            if (successType == AiStreamType.Title){
                updateFlowState(aiFlow = AiFlow.StreamSuccess(streamType = successType))
            }else{
                requestAiCheckInfo(workId = workId, workStateId = workStateId)
            }
        }
    }

    suspend fun retryAiStream(
        workId: Long,
        workStateId: Long,
        type: AiStreamType = cachedStreamType,
    ) {
        updateStreamType(type)
        requestAiStreamInfoSuspend(
            workId = workId,
            workStateId = workStateId,
            aiStreamStatus = AiStreamStatus.Failed,
            aiStreamType = type
        )
    }

    /**
     * 请求AI流数据
     */
    private suspend fun requestAiStreamInfoSuspend(
        workId: Long,
        workStateId: Long,
        aiStreamStatus: AiStreamStatus,
        aiStreamType: AiStreamType,
    ) {
        HLog.d(
            HTag.TAG,
            "准备请求AI流: ${workId}_${workStateId}; aiStreamStatus: ${aiStreamStatus}; aiStreamType:${aiStreamType}"
        )
        updateStreamType(aiStreamType)
        delay(1_000L)
        // 需要等待一会再启动AI流
        var job: Job? = null
        coroutineScope {
            job = launchOnHttp(onError = {
                requestError()
                job?.cancel()
            }) {
                //
                requestAiStreamInfoByType(
                    workId = workId,
                    workStateId = workStateId,
                    aiStreamStatus = aiStreamStatus,
                    aiStreamType = aiStreamType,
                )?.collect { streamDetail ->
                    streamDetail?.let {
                        if (it.isSuccess()) {
                            cachedStreamDetail = it
                        }
                        updateFlowState(
                            Streaming(
                                streamDetail = streamDetail,
                                streamType = aiStreamType
                            )
                        )
                        when {
                            streamDetail.isStreamError() -> {
                                job?.cancel()
                            }

                            streamDetail.isCompletion() -> {
                                job?.cancel()
                            }
                        }
                    }
                } ?: job?.cancel()
            }
        }
        HLog.i(HTag.TAG, "等待 AI流 end")
    }

    private fun requestAiStreamInfoByType(
        workId: Long,
        workStateId: Long,
        aiStreamStatus: AiStreamStatus,
        aiStreamType: AiStreamType,
    ): Flow<AiStreamDetail?>? {
        return when (aiStreamType) {
            AiStreamType.Title -> {
                repository.requestTopicAiStreamInfo(
                    sseClient = aiStreamClient,
                    workId = workId,
                    workStateId = workStateId,
                    aiStreamStatus = aiStreamStatus
                )
            }

            AiStreamType.First -> {
                repository.requestFirstAiStreamInfo(
                    sseClient = aiStreamClient,
                    workId = workId,
                    workStateId = workStateId,
                    aiStreamStatus = aiStreamStatus
                )
            }

            AiStreamType.Second -> {
                repository.requestSecondAiStreamInfo(
                    sseClient = aiStreamClient,
                    workId = workId,
                    workStateId = workStateId,
                    aiStreamStatus = aiStreamStatus
                )
            }

            else -> {
                null
            }
        }
    }

    private fun updateStreamType(newType: AiStreamType) {
        cachedStreamType = newType
    }

    /**
     * 请求AI批改结果
     */
    suspend fun requestAiCheckInfo(workId: Long, workStateId: Long) {
        HLog.i(HTag.TAG, "请求AI批改结果: workId: $workId, workStateId:$workStateId")
        updateFlowState(AiFlow.Loading)
        val workState = workReposition.queryWorkState(workId = workId, workStateId = workStateId)
        // 是否已提交，未提交请求一稿AI批改，未提交请求二稿AI批改
        val isSubmitted = (workState?.state ?: 0) >= WorkStatus.ALL_SUBMITTED
        val existsAiCheckDetail = repository.queryAiCheckDetail(
            workId = workId,
            workStateId = workStateId,
            isSubmitted = isSubmitted,
        )
        // 检查本地是否有已成功的数据
        if (existsAiCheckDetail?.isDirty() == false) {
            // 本地已有成功的数据，数据返回
            HLog.i(HTag.TAG, "本地存在批改成功数据，直接返回!")
            updateFlowState(AiFlow.AllSuccess(existsAiCheckDetail))
            return
        }
        //
        // 获取远程数据
        val remoteCheckContent = repository.requestAiCheckInfo(
            workId = workId,
            workStateId = workStateId,
            isSubmitted = isSubmitted
        )
        if (remoteCheckContent == null) {
            HLog.i(HTag.TAG, "获取批改结果失败)")
            updateFlowState(AiFlow.Reload)
            return
        }
        //
        val jobStatus = remoteCheckContent.getJobStatus()
        val ocrStatus = remoteCheckContent.getOcrJobStatus()
        val scoreJobStatus = remoteCheckContent.getScoreJobStatus()
        val allusionJobStatus = remoteCheckContent.getAllusionJobStatus()
        val errorMessage = remoteCheckContent.message
        val newAiCheckContent = repository.queryAiCheckDetail(
            workId = workId,
            workStateId = workStateId,
            isSubmitted
        )?.apply {
            this.allusionJobStatus = allusionJobStatus
            this.scoreJobStatus = scoreJobStatus
        }
        when {
            ocrStatus.isFailed() -> {
                // OCR失败
                updateFlowState(AiFlow.OcrFailed(errorMessage = errorMessage))
            }

            jobStatus.isFailed() -> { // 其他失败，显示重试
                updateFlowState(AiFlow.Reload)
            }

            scoreJobStatus.isRunning() -> {
                HLog.i(HTag.TAG, "分数计算中~~")
                updateFlowState(AiFlow.BackgroundRunning(newAiCheckContent))
            }

            allusionJobStatus.isRunning() -> {
                HLog.i(HTag.TAG, "加油站生成中~~")
                updateFlowState(AiFlow.BackgroundRunning(newAiCheckContent))
            }

            else -> { // 成功
                updateFlowState(AiFlow.AllSuccess(newAiCheckContent))
            }
        }
    }

    // ----------------------------------------------------------------------
    private suspend fun requestError() {
        HLog.Companion.i(HTag.TAG, "请求AI流失败")
        updateFlowState(AiFlow.RequestError(streamDetail = getStreamDetail()))
    }

    private fun getStreamDetail(): AiStreamDetail {
        return cachedStreamDetail ?: AiStreamDetail.Companion.obtainErrorDetail()
    }

    private suspend fun updateFlowState(aiFlow: AiFlow) {
        aiFlowState.emit(aiFlow)
    }

    // ----------------------------------------------------------------------
    sealed interface AiFlow {
        object Default : AiFlow
        data class TitleOcrPreparing(val ocrCreateTimeMillis: Long) : AiFlow
        data class TitleOrcFailed(val compositionStatusResponse: CompositionStatusResponse,
                                  val errorMessage: String?) : AiFlow
        data object TitleOcrSuccess : AiFlow

        //
        data class ContentOcrPreparing(val ocrCreateTimeMillis: Long) : AiFlow
        data class OcrFailed(val errorMessage: String?) : AiFlow

        // ----------------------------------------------------------------------
        data class RequestError(val streamDetail: AiStreamDetail) : AiFlow

        //
        data class StreamPreparing(val streamType: AiStreamType) : AiFlow

        //        data class StreamReady(val streamType: AiStreamType) : AiFlow
        data class Streaming(val streamDetail: AiStreamDetail, val streamType: AiStreamType) :
            AiFlow

        data class StreamError(val streamType: AiStreamType) : AiFlow
        data class StreamRunning(val streamType: AiStreamType) : AiFlow
        data class StreamSuccess(val streamType: AiStreamType) : AiFlow

        //
        data object Loading : AiFlow
        data object Reload : AiFlow
        data class BackgroundRunning(val aiCheckContent: CompositionCheckDetail?) : AiFlow

        //
        data class WaitingSecondStream(val aiCheckContent: CompositionCheckDetail?) : AiFlow
        data class AllSuccess(val aiCheckContent: CompositionCheckDetail?) : AiFlow
        data object ScoreFailed : AiFlow
        data object AllusionFailed : AiFlow

        /**
         * 流的前置条件是否已经准备完毕
         * ocr 完成
         */
        fun topicPrepared(): Boolean {
            return when (this) {
                is Default -> false
                is TitleOcrPreparing, is TitleOrcFailed -> false
                else -> true
            }
        }
    }

    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------

    private suspend fun mock() {
        val reasonBuilder = StringBuilder()
        val answerBuilder = StringBuilder()
        repeat(100) {
            reasonBuilder.append("这是一段测试数据，用于模拟流式输出。\n")
            answerBuilder.append("## 标题${it} \n - **这是**一段测试数据\n 用于模拟流式输出。\n")
            delay(100)
            updateFlowState(
                AiFlow.Streaming(
                    streamDetail = AiStreamDetail(
                        reasoning = reasonBuilder.toString(),
                        answering = answerBuilder.toString(),
                        finishReason = null,
                    ),
                    streamType = AiStreamType.Second
                )
            )
        }

//        updateFlowState(
//            AiFlow.Preparing(
//                ocrCreateTimeMillis = DateUtil.stringToDate(
//                    dateStr = DateUtil.timeMillisToString(System.currentTimeMillis() / 60_000L * 60_000L),
//                    pattern = DateUtil.DEFAULT_PATTERN
//                )?.time ?: System.currentTimeMillis()
//            )
//        )
    }
}