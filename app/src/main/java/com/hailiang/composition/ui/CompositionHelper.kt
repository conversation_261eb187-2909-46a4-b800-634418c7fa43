package com.hailiang.composition.ui

import com.hailiang.hlutil.ext.jsonToList
import com.hailiang.view.question.composition.Composition
import com.hailiang.workcloud.data.vo.CompositionPractice

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/16 02:53
 */
object CompositionHelper {

    fun obtainCompositionTableBean(compositionPractice: CompositionPractice?): Composition {
        val composition = Composition(compositionPractice?.secondPracticeTitle ?: "", 20, 2)
        compositionPractice?.secondPracticeContent?.jsonToList(String::class.java)
            ?.forEach { paragraph ->
                composition.appendParagraph(paragraph)
            }
        return composition
    }
}