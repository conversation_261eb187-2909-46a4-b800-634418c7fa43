package com.hailiang.composition.ui.widget

import androidx.compose.foundation.Image
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.runtime.Composable
import androidx.compose.runtime.remember
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.graphics.ColorFilter
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.res.colorResource
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.constraintlayout.compose.ConstraintLayout
import com.hailiang.common.compose.theme.AppColors
import com.hailiang.composition.ui.practice.CompositionStep
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.xxb.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/20 21:15
 */


// ----------------------------------------------------------------------
@Composable
fun CompositionStepLayout(
    modifier: Modifier,
    selectedStep: CompositionStep,
    activatedStep: CompositionStep,
    allSteps: List<CompositionStep>,
    onTabClicked: (CompositionStep) -> Unit,
) {
    Row(
        modifier = modifier,
        horizontalArrangement = Arrangement.spacedBy(20.dp)
    ) {
        val selectedStepIndex = selectedStep.step
        val activatedStepIndex = activatedStep.step
//        HLog.i(
//            HTag.TAG,
//            "CompositionStepLayout: selectedStep: $selectedStep; activatedStep: $activatedStep"
//        )
        allSteps.forEachIndexed { index, item ->
            if (index > 0) {
                Image(
                    modifier = Modifier
                        .height(14.dp)
                        .align(Alignment.CenterVertically),
                    painter = painterResource(
                        if (index <= activatedStepIndex) {
                            R.drawable.ic_arrow_ai_step_selected
                        } else {
                            R.drawable.ic_arrow_ai_step_unselected
                        }
                    ),
                    contentDescription = null
                )
            }
            CompositionStepItem(
                itemStep = item,
                selected = selectedStepIndex == item.step,
                activated = activatedStepIndex >= item.step,
                onTabClicked = onTabClicked
            )
        }
    }
}

@Composable
private fun CompositionStepItem(
    itemStep: CompositionStep,
    selected: Boolean,
    activated: Boolean,
    onTabClicked: (CompositionStep) -> Unit,
) {
    ConstraintLayout(
        modifier = Modifier
            .height(56.dp)
            .clickable(
                enabled = activated && itemStep.fragment() != null,
                indication = null,
                interactionSource = remember { MutableInteractionSource() },
                onClick = {
                    onTabClicked.invoke(itemStep)
                }
            )
    ) {
        val (textIcon, indicator) = createRefs()
        Image(
            modifier = Modifier
                .height(29.dp)
                .constrainAs(textIcon) {
                    top.linkTo(parent.top)
                    bottom.linkTo(parent.bottom)
                    start.linkTo(parent.start)
                },
            painter = painterResource(itemStep.stepIcon),
            contentScale = ContentScale.FillHeight,
            contentDescription = null,
            colorFilter = ColorFilter.tint(
                if (activated) AppColors.TextBlack else colorResource(com.hailiang.xxb.resource.R.color.black_opaque_40)
            )
        )
        if (selected) {
            Box(
                modifier = Modifier
                    .size(width = 50.dp, height = 6.dp)
                    .background(
                        brush = Brush.linearGradient(
                            0.0f to Color(0xFFBF9DF5),
                            1.0f to Color(0xFF3968FF)
                        ),
                        shape = RoundedCornerShape(4.dp)
                    )
                    .constrainAs(indicator) {
                        top.linkTo(textIcon.bottom, margin = 4.dp)
                        start.linkTo(anchor = textIcon.start)
                        end.linkTo(anchor = textIcon.end)
                    }
            )
        }
    }
}

// ----------------------------------------------------------------------

@Composable
@Preview(widthDp = 800)
private fun CompositionToolbarPreview() {
    Column(modifier = Modifier.background(Color(0xFF00F0F0))) {
        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.CheckFirstPractice,
            activatedStep = CompositionStep.CheckFirstPractice,
            allSteps = listOf(
                CompositionStep.CheckQuestion,
                CompositionStep.CheckFirstPractice,
                CompositionStep.CheckSecondPractice,
            ),
            onTabClicked = {}
        )

        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.FirstPractice,
            activatedStep = CompositionStep.AiGuidance,
            allSteps = listOf(
                CompositionStep.FirstPractice,
                CompositionStep.AiGuidance,
                CompositionStep.SecondPractice,
            ),
            onTabClicked = {}
        )

        CompositionStepLayout(
            modifier = Modifier,
            selectedStep = CompositionStep.CheckQuestion,
            activatedStep = CompositionStep.CheckSecondPractice,
            allSteps = listOf(
                CompositionStep.CheckQuestion,
                CompositionStep.CheckFirstPractice,
                CompositionStep.CheckSecondPractice,
            ),
            onTabClicked = {}
        )
    }
}