package com.hailiang.composition.ui.practice.theme

import androidx.compose.ui.unit.sp

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/15 18:57
 */
object CompositionFonts {

    val AiCommonFontSize = 16.sp
    val AiTitleFontSize = 16.sp
    val TitleFontSize = 22.sp

//    val BangBangFontFamily = FontFamily(
//        Font(resId = R.font.bangbang),
//        Font(resId = R.font.bangbang, weight = FontWeight.Bold),
//    )
//    val HongLeiFontFamily = FontFamily(
//        Font(resId = R.font.honglei),
//        Font(resId = R.font.honglei, weight = FontWeight.Bold),
//    )
}