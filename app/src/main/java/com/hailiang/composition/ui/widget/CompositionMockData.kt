package com.hailiang.composition.ui.widget

import com.hailiang.composition.ui.practice.AiSectorComment
import com.hailiang.composition.ui.practice.AiSubSector

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/11 13:56
 */
internal object CompositionMockData {
    fun mockAiSubSectorList(): List<AiSubSector> {
        return listOf(
            AiSubSector.Advantage(
                comments = emptyList()
            ),
            AiSubSector.Advantage(
                comments = listOf(
                    AiSectorComment.Advantage(
                        comment = "11111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111111"
                    ),
                )
            ),
            AiSubSector.Suggestion(
                comments = listOf(
                    AiSectorComment.Suggestion(comment = "Suggestion")
                )
            ),
            AiSubSector.Title(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Title Advantage"),
                    AiSectorComment.Suggestion(comment = "Title Suggestion")
                )
            ),
            AiSubSector.Structure(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Structure AdvantageStructure AdvantageStructure AdvantageStructure AdvantageStructure AdvantageStructure 123123123123123123123123123123123123123123123123123123123112312312312312312312312312312312312312312312312312312323123123123123123123123123123123123123123123123123123123123123123123"),
                    AiSectorComment.Suggestion(comment = "Structure Suggestion")
                )
            ),
            AiSubSector.Argument(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Argument Advantage"),
                    AiSectorComment.Suggestion(comment = "Argument Suggestion")
                )
            ),
            AiSubSector.BeginningAndEnd(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "BeginningAndEnd Advantage"),
                    AiSectorComment.Suggestion(comment = "BeginningAndEnd Suggestion")
                )
            ),
            AiSubSector.Sentence(
                comments = listOf(
                    AiSectorComment.Advantage(
                        content = "第一句",
                        comment = "Sentence Advantage"
                    ),
                    AiSectorComment.Suggestion(
                        content = "第二句",
                        comment = "Sentence Suggestion"
                    )
                )
            ),
            AiSubSector.History(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "History Advantage"),
                    AiSectorComment.Suggestion(comment = "History Suggestion")
                )
            ),
            AiSubSector.Reality(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "Reality Advantage"),
                    AiSectorComment.Suggestion(comment = "Reality Suggestion")
                )
            ),
            AiSubSector.RefinedSentence(
                comments = listOf(
                    AiSectorComment.Advantage(comment = "RefinedSentence Advantage"),
                    AiSectorComment.Suggestion(comment = "RefinedSentence Suggestion")
                )
            )
        )
    }
}