package com.hailiang.composition.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.Paint
import android.graphics.PorterDuff
import android.graphics.PorterDuffXfermode

import android.util.AttributeSet
import android.view.View
import com.hailiang.hlutil.dp

class TransparentOverlayWithBorder(context: Context, attrs: AttributeSet?)
    : View(context, attrs) {

    private val overlayPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#80000000") // 半透明黑色背景
        style = Paint.Style.FILL
    }

    private val borderPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#A622CCFF")
        style = Paint.Style.STROKE
        strokeWidth = 1f.dp // 边框宽度调整为4dp
        strokeJoin = Paint.Join.ROUND
    }

    private val clearPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        style = Paint.Style.FILL
        xfermode = PorterDuffXfermode(PorterDuff.Mode.CLEAR)
    }
    // 新增角标绘制参数
    private val cornerPaint = Paint(Paint.ANTI_ALIAS_FLAG).apply {
        color = Color.parseColor("#A622CCFF")
        style = Paint.Style.STROKE
        strokeWidth = 4f.dp  // 角标线宽
        strokeCap = Paint.Cap.SQUARE
    }

    private val cornerLength = 48f.dp  // 角标线长度

    private var margin = 40f.dp

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)

        // 创建离屏图层（必须包含背景绘制操作）
        val savedLayer = canvas.saveLayer(0f, 0f, width.toFloat(), height.toFloat(), null)

        // 步骤1：先绘制半透明背景
        canvas.drawRect(0f, 0f, width.toFloat(), height.toFloat(), overlayPaint)

        // 步骤2：在同一个图层上执行清除操作
        canvas.drawRect(
            margin,
            margin,
            width - margin,
            height - margin,
            clearPaint
        )

        // 恢复图层（此时透明区域已生效）
        canvas.restoreToCount(savedLayer)

        // 步骤3：最后绘制边框（在最终画布上绘制）
        canvas.drawRect(
            margin,
            margin,
            width - margin,
            height - margin,
            borderPaint
        )

        // 新增：绘制四个角标
        drawCorners(canvas)
    }

    private fun drawCorners(canvas: Canvas) {
        val left = margin
        val top = margin
        val right = width - margin
        val bottom = height - margin

        // 左上角
        canvas.drawLine(
            left, top,
            left + cornerLength, top,
            cornerPaint
        )
        canvas.drawLine(
            left, top,
            left, top + cornerLength,
            cornerPaint
        )

        // 右上角
        canvas.drawLine(
            right - cornerLength, top,
            right, top,
            cornerPaint
        )
        canvas.drawLine(
            right, top,
            right, top + cornerLength,
            cornerPaint
        )

        // 右下角
        canvas.drawLine(
            right - cornerLength, bottom,
            right, bottom,
            cornerPaint
        )
        canvas.drawLine(
            right, bottom - cornerLength,
            right, bottom,
            cornerPaint
        )

        // 左下角
        canvas.drawLine(
            left, bottom - cornerLength,
            left, bottom,
            cornerPaint
        )
        canvas.drawLine(
            left, bottom,
            left + cornerLength, bottom,
            cornerPaint
        )
    }

}


