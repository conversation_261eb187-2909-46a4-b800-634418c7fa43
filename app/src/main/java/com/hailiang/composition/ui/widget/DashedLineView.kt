package com.hailiang.composition.ui.widget

import android.content.Context
import android.graphics.Canvas
import android.graphics.Color
import android.graphics.DashPathEffect
import android.graphics.Paint
import android.util.AttributeSet
import android.view.View
import com.hailiang.xxb.composition.R

class DashedLineView @JvmOverloads constructor(
    context: Context, attrs: AttributeSet? = null, defStyleAttr: Int = 0
) : View(context, attrs, defStyleAttr) {

    private val paint = Paint().apply {
        color = Color.WHITE
        style = Paint.Style.STROKE
        strokeWidth = 20f
        pathEffect = DashPathEffect(floatArrayOf(10f, 6f), 0f) // 默认 dashWidth 和 dashGap
    }

    private var orientation = 0

    init {
        context.theme.obtainStyledAttributes(
            attrs,
            R.styleable.DashedLineView,
            0, 0
        ).apply {
            try {
                val dashWidth = getDimension(<PERSON>.styleable.DashedLineView_dashWidth, 15f)
                val dashGap = getDimension(R.styleable.DashedLineView_dashGap, 9f)
                orientation =
                    getInt(R.styleable.DashedLineView_orientation, 0) // default to vertical
                paint.pathEffect = DashPathEffect(floatArrayOf(dashWidth, dashGap), 0f)
            } finally {
                recycle()
            }
        }
    }

    override fun onDraw(canvas: Canvas) {
        super.onDraw(canvas)
        if (orientation == 0) { // vertical
            val widthCenter = width / 2f
            canvas.drawLine(widthCenter, 0f, widthCenter, height.toFloat(), paint)
        } else if (orientation == 1) { // horizontal
            val heightCenter = height / 2f
            canvas.drawLine(0f, heightCenter, width.toFloat(), heightCenter, paint)
        }
    }
}