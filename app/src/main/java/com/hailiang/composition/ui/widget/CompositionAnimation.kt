package com.hailiang.composition.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Arrangement
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Column
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.foundation.layout.wrapContentSize
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.DisposableEffect
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.geometry.Offset
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.layout.ContentScale
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.airbnb.lottie.LottieCompositionFactory
import com.airbnb.lottie.compose.LottieAnimation
import com.airbnb.lottie.compose.LottieCompositionSpec
import com.airbnb.lottie.compose.LottieConstants
import com.airbnb.lottie.compose.animateLottieCompositionAsState
import com.airbnb.lottie.compose.rememberLottieComposition
import com.hailiang.common.compose.theme.AppColors
import com.hailiang.composition.ui.practice.theme.CompositionFonts
import com.hailiang.xxb.composition.R


/**
 * AI批改中
 */
@Composable
fun AiCorrectingAnimation(
    modifier: Modifier = Modifier,
) {
    Column(
        modifier = modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFFFFFFFF),
                        1.0f to Color(0xFFE5E8FF),
                    ),
                    start = Offset(0F, 0F),
                    end = Offset(0f, Float.POSITIVE_INFINITY)
                )
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "预估得分 生成中",
            fontSize = 18.sp,
            color = AppColors.TextBlack,
            fontWeight = FontWeight.Bold
        )
        MyLottieAnimation(
            modifier = Modifier
                .width(225.dp)
                .height(225.dp),
            spec = LottieCompositionSpec.RawRes(R.raw.lottie_ai_assistant),
        )
        Text(
            text = "评分具有主观性，以教师批改结果为准哦~",
            fontSize = 18.sp,
            color = AppColors.TextBlack,
            fontWeight = FontWeight.Bold
        )
    }
}

/**
 * AI识别中
 */
@Composable
fun AiLoadingAnimation() {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .background(
                brush = Brush.linearGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFFFFFFFF),
                        1.0f to Color(0xFFE5E8FF),
                    ),
                    start = Offset(0F, 0F),
                    end = Offset(0f, Float.POSITIVE_INFINITY)
                )
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = "智能点拨助手努力工作中",
            fontSize = CompositionFonts.AiCommonFontSize,
            color = Color(0xFF515A6E)
        )
        MyLottieAnimation(
            modifier = Modifier
                .width(225.dp)
                .height(225.dp),
            spec = LottieCompositionSpec.RawRes(R.raw.lottie_ai_identifying),
        )
        Text(
            text = "作文已保存，稍后再来吧～",
            fontSize = CompositionFonts.AiCommonFontSize,
            color = AppColors.TextBlack,
            fontWeight = FontWeight.Bold
        )
    }
}

@Composable
fun AiThinkingAnimation() {
    MyLottieAnimation(
        modifier = Modifier
            .width(30.dp)
            .height(30.dp),
        spec = LottieCompositionSpec.RawRes(R.raw.lottie_ai_identifying),
    )
}

/**
 * OCR识别中
 */
@Composable
fun OcrLoadingAnimation(
    progress: Int,
    hint: String = "题目及作文识别中…",
) {
    Column(
        modifier = Modifier
            .fillMaxSize()
            .clip(RoundedCornerShape(CORNER_RADIUS))
            .background(
                brush = Brush.linearGradient(
                    colorStops = arrayOf(
                        0.0f to Color(0xFFFFFFFF),
                        1.0f to Color(0xFFE5E8FF),
                    ),
                    start = Offset(0F, 0F),
                    end = Offset(0f, Float.POSITIVE_INFINITY)
                )
            ),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        Text(
            text = hint,
            fontSize = CompositionFonts.AiCommonFontSize,
            color = AppColors.TextBlack,
        )
        Spacer(modifier = Modifier.size(10.dp))
        CircularProgressBar(progress = progress)
    }
}


@Composable
fun LoadingAnimation() {
    Column(
        modifier = Modifier.fillMaxSize(),
        horizontalAlignment = Alignment.CenterHorizontally,
        verticalArrangement = Arrangement.Center
    ) {
        MyLottieAnimation(
            modifier = Modifier.size(160.dp),
            spec = LottieCompositionSpec.RawRes(R.raw.lottie_loading),
            speed = 2F
        )
        Text("加载中", color = Color(0xFF515A6E), fontSize = CompositionFonts.AiCommonFontSize)
    }
}

/**
 * 扫描动画
 */
@Composable
fun ScannerAnimation(
    modifier: Modifier = Modifier,
) {
    MyLottieAnimation(
        modifier = modifier.fillMaxWidth(),
        spec = LottieCompositionSpec.RawRes(R.raw.lottie_scanner),
        contentScale = ContentScale.FillWidth
    )
}

/**
 * 点赞动画
 */
@Composable
fun PraiseAnimation(
    modifier: Modifier = Modifier,
    isPlaying: Boolean,
    onFinished: () -> Unit,
) {
    MyLottieAnimation(
        modifier = modifier,
        spec = LottieCompositionSpec.RawRes(R.raw.lottie_praise),
        iterations = 1,
        initPlaying = isPlaying,
        speed = 6F,
        onFinished = onFinished,
    )
}

// ----------------------------------------------------------------------
@Composable
private fun MyLottieAnimation(
    modifier: Modifier = Modifier,
    spec: LottieCompositionSpec,
    iterations: Int = LottieConstants.IterateForever,
    initPlaying: Boolean = true,
    speed: Float = 1F,
    contentScale: ContentScale = ContentScale.Fit,
    onFinished: () -> Unit = {},
) {
    Box(
        modifier = Modifier.wrapContentSize(),
        contentAlignment = Alignment.Center
    ) {
        val context = LocalContext.current
        DisposableEffect(Unit) {
            onDispose {
                LottieCompositionFactory.clearCache(context)
            }
        }

        val composition by rememberLottieComposition(spec)
        val progress by animateLottieCompositionAsState(
            composition = composition,
            iterations = iterations,
            isPlaying = initPlaying,
            speed = speed
        )
        var isPlaying by remember { mutableStateOf(initPlaying) }
        // 监听动画结束
        LaunchedEffect(progress) {
            if (progress >= 0.99f && isPlaying) { // 接近 1 表示动画结束
                onFinished()
                isPlaying = false
            }
        }

        LottieAnimation(
            composition = composition,
            progress = { progress },
            modifier = modifier,
            contentScale = contentScale,
        )
    }
}

@Composable
@Preview
private fun AiThinkingAnimationPreview() {
    Box(modifier = Modifier.background(color = Color.White)) {
        AiThinkingAnimation()
    }
}

@Composable
@Preview
private fun AnimationPreview() {
    LoadingAnimation()
}

@Composable
@Preview
private fun AiCorrectingAnimationPreview() {
    AiCorrectingAnimation()
}

@Composable
@Preview
private fun OcrLoadingAnimationPreview() {
    OcrLoadingAnimation(50)
}

@Composable
@Preview
private fun PraiseAnimationPreview() {
    Box(modifier = Modifier.background(color = Color.White)) {
        PraiseAnimation(isPlaying = true, onFinished = {})
    }
}
