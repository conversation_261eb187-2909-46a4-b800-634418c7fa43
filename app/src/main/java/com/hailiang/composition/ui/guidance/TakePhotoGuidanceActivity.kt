package com.hailiang.composition.ui.guidance

import android.content.pm.ActivityInfo
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.padding
import androidx.compose.material3.ExperimentalMaterial3Api
import androidx.compose.material3.Scaffold
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Brush
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.lifecycle.lifecycleScope
import com.hailiang.common.base.BaseActivity
import com.hailiang.common.compose.theme.BackIcon
import com.hailiang.composition.data.Repository
import com.hailiang.composition.util.SpManager
import com.hailiang.hlutil.launchCatch

class TakePhotoGuidanceActivity : BaseActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        requestedOrientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
        super.onCreate(savedInstanceState)
        setContent {
            TakePhotoGuidanceScreen(
                onBackClick = { finish() },
                onOkClick = { finish() }
            )
        }

        //更新引导阅读状态
        lifecycleScope.launchCatch {
            val result = Repository().addBeginnerGuidance()
            if (result.isSuccess) {
                SpManager.markTakePhotoBeginnerGuidanceRead()
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
private fun TakePhotoGuidanceScreen(
    onBackClick: () -> Unit,
    onOkClick: () -> Unit,
) {
    Scaffold(
        modifier = Modifier.background(
            brush = Brush.linearGradient(
                0.0f to Color(0xFFDFE9FF),
                1.0f to Color(0xFFE3F3FF),
            )
        ),
        containerColor = Color.Transparent,
    ) { innerPadding ->
        Box(modifier = Modifier.padding(innerPadding)) {
            AiGuidanceLayout(onOkClick)
            BackIcon(60.dp, 60.dp, onBackClick)
        }
    }
}

@Preview(apiLevel = 33, widthDp = 1280)
@Composable
private fun CompositionAiGuidancePreview() {
    TakePhotoGuidanceScreen(onBackClick = {}, onOkClick = {})
}