package com.hailiang.composition.ui.practice

import android.content.Context
import android.content.Intent
import android.graphics.drawable.GradientDrawable
import android.os.Bundle
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.core.graphics.toColorInt
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.airbnb.lottie.LottieCompositionFactory
import com.hailiang.common.base.BaseActivity
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.hlutil.ActivityUtil
import com.hailiang.hlutil.HLog

class CompositionGuidanceActivity : BaseActivity() {
    private val practiceViewModel by viewModels<CompositionPracticeViewModel>()
    private val checkViewModel by viewModels<CompositionCheckViewModel>()
    private val compositionViewModel by viewModels<CompositionViewModel>()

    companion object {
        private const val WORK_ID = "workId"
        private const val WORK_STATE_ID = "workStateId"

        fun start(context: Context, workId: Long, workStateId: Long) {
            val intent = Intent(context, CompositionGuidanceActivity::class.java)
            intent.putExtra(WORK_ID, workId)
            intent.putExtra(WORK_STATE_ID, workStateId)
            ActivityUtil.startActivity(context, intent)
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        val gradientDrawable = GradientDrawable(
            GradientDrawable.Orientation.LEFT_RIGHT,
            intArrayOf("#FFDFE9FF".toColorInt(), "#FFE3F3FF".toColorInt())
        )
        window.decorView.background = gradientDrawable
        val workId = intent.getLongExtra(WORK_ID, 0)
        val workStateId = intent.getLongExtra(WORK_STATE_ID, 0)
        // ----------------------------------------------------------------------
        compositionViewModel.aiWorkFlow.launchAndCollect(this) {
            // 接收AI工作流程的响应事件
        }
        compositionViewModel.compositionTableState.launchAndCollect(this) { status ->
            if (status is CompositionTableState.Success) {
                checkViewModel.diffCompositionContent(status)
            }
        }
        // ----------------------------------------------------------------------
        setContent {
            val compositionState by compositionViewModel.compositionContentState.collectAsStateWithLifecycle()
            val selectedStep by compositionViewModel.compositionStepState.collectAsStateWithLifecycle()
            val isShowKeyBoard by compositionViewModel.isShowKeyBoard.collectAsStateWithLifecycle()

            LaunchedEffect("${compositionState.workId}_${compositionState.workStateId}") {
                compositionViewModel.loadStep()
            }
            CompositionScreen(
                selectedStep = selectedStep,
                onTabClicked = { compositionViewModel.switchStep(it) },
                onBackPressed = { finish() },isShowKeyBoard = isShowKeyBoard
            )
        }
        practiceViewModel.init()
        compositionViewModel.init(workId = workId, workStateId = workStateId)
        compositionViewModel.loadCompositionWork()
        compositionViewModel.requestAiStreamStatus()
        compositionViewModel.startCounting()
    }

    override fun onRestart() {
        super.onRestart()
        compositionViewModel.resetCounting()
    }

    override fun onStop() {
        super.onStop()
        compositionViewModel.saveAndRestCounting()
        compositionViewModel.reportSwitchStepEvent()
        compositionViewModel.reportAiTabSwitchEvent()
    }

    override fun onDestroy() {
        super.onDestroy()
        LottieCompositionFactory.clearCache(this)
    }
}


