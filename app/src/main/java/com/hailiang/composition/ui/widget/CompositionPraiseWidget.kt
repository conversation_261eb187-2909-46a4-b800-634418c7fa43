package com.hailiang.composition.ui.widget

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.Row
import androidx.compose.foundation.layout.Spacer
import androidx.compose.foundation.layout.fillMaxWidth
import androidx.compose.foundation.layout.height
import androidx.compose.foundation.layout.size
import androidx.compose.foundation.layout.width
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.runtime.LaunchedEffect
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.graphics.Color
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import com.hailiang.composition.ui.practice.AiResponseState
import com.hailiang.composition.ui.practice.AiSector
import com.hailiang.composition.ui.practice.EvaluateFeedback
import com.hailiang.composition.ui.practice.EvaluateFeedbackState

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/19 09:27
 */
@Composable
fun CompositionPraiseWidget(
    aiResponseState: AiResponseState,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    if (aiResponseState !is AiResponseState.Success) {
        return
    }
    CompositionPraiseWidget(
        selectedAiSector = aiResponseState.selectedAiSector,
        initFeedback = initFeedback,
        feedbackClick = feedbackClick
    )
}

@Composable
fun CompositionPraiseWidget(
    selectedAiSector: AiSector,
    initFeedback: EvaluateFeedbackState? = null,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)? = null,
) {
    CompositionPraiseWidget(
        selectedAiSector = selectedAiSector,
        initFeedback = initFeedback?.feedbackMap?.get(selectedAiSector),
        needAnimation = initFeedback?.needAnimation ?: false,
        feedbackClick = feedbackClick
    )
}

@Composable
fun CompositionPraiseWidget(
    selectedAiSector: AiSector,
    initFeedback: EvaluateFeedback?,
    needAnimation: Boolean,
    feedbackClick: ((AiSector, EvaluateFeedback, Boolean) -> Unit)?,
) {
    var animationVisible by remember { mutableStateOf(needAnimation) }
    LaunchedEffect(needAnimation) { // tab发生变化时，关闭动画
        animationVisible = needAnimation
    }
    Row(
        modifier = Modifier
            .height(48.dp)
            .background(Color.White)
            .fillMaxWidth(),
        verticalAlignment = Alignment.CenterVertically
    ) {
        Spacer(modifier = Modifier.width(30.dp))
        Text(
            text = "AI评价的如何？", color = Color(0xFF515A6E),
            fontSize = 16.sp
        )
        Spacer(modifier = Modifier.width(8.dp))
        Box {
            if (animationVisible) {
                PraiseAnimation(
                    modifier = Modifier.size(40.dp),
                    isPlaying = true,
                    onFinished = {
                        animationVisible = false
                    })
            } else {
                FeedbackIcon(
                    evaluateFeedback = EvaluateFeedback.Like,
                    selectedFeedback = initFeedback,
                    onClick = { feedback, isSelected ->
                        feedbackClick?.invoke(selectedAiSector, feedback, isSelected)
//                        if (feedbackClick != null) {
//                            animationVisible = isSelected
//                        }
                    }
                )
            }
        }
        Spacer(modifier = Modifier.width(10.dp))
        FeedbackIcon(
            evaluateFeedback = EvaluateFeedback.Dislike,
            selectedFeedback = initFeedback,
            onClick = { feedback, isSelected ->
                animationVisible = false
                feedbackClick?.invoke(selectedAiSector, feedback, isSelected)
            }
        )
    }
}

@Composable
private fun FeedbackIcon(
    evaluateFeedback: EvaluateFeedback,
    selectedFeedback: EvaluateFeedback? = null,
    clickable: Boolean = true,
    onClick: (EvaluateFeedback, Boolean) -> Unit,
) {
    CompositionSelectedIcon(
        modifier = Modifier.size(40.dp),
        normalRes = evaluateFeedback.normalRes,
        selectedRes = evaluateFeedback.selectedRes,
        isSelected = evaluateFeedback == selectedFeedback,
        onClick = if (clickable) {
            {
                onClick(evaluateFeedback, evaluateFeedback != selectedFeedback)
            }
        } else {
            null
        }
    )
}

@Composable
@Preview
private fun CompositionPraiseWidgetPreview() {
    CompositionPraiseWidget(
        selectedAiSector = AiSector.Evaluate,
        initFeedback = EvaluateFeedback.Like,
        needAnimation = false,
        feedbackClick = { _, _, _ -> }
    )
}