package com.hailiang.composition.ui.widget

import androidx.appcompat.app.AppCompatActivity
import androidx.compose.foundation.layout.Box
import androidx.compose.foundation.layout.fillMaxSize
import androidx.compose.material3.Text
import androidx.compose.runtime.Composable
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalInspectionMode
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.viewinterop.AndroidView
import androidx.fragment.app.Fragment
import androidx.fragment.app.FragmentContainerView
import com.hailiang.core.ext.replaceFragment
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.xxb.composition.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:07
 */
@Composable
fun FragmentInCompose(fragment: Fragment) {
//    HLog.i(HTag.TAG, "FragmentInCompose: fragment; $fragment")
    if (LocalInspectionMode.current) {
        // Preview 模式下显示占位符
        Box(
            modifier = Modifier.fillMaxSize(),
        ) {
            Text(
                text = "AndroidView is not supported in Preview",
                modifier = Modifier.align(Alignment.Center),
                textAlign = TextAlign.Center
            )
        }
        return
    }
    AndroidView(
        factory = { context ->
            FragmentContainerView(context).apply {
                id = R.id.compose_inner_fragment_container
            }
        },
        modifier = Modifier.fillMaxSize(),
        update = { view ->
            (view.context as AppCompatActivity).replaceFragment(
                R.id.compose_inner_fragment_container,
                fragment
            )
            val activity = view.context as AppCompatActivity
            val fragmentManager = activity.supportFragmentManager
            val fragmentTag = fragment.javaClass.name

            val existingFragment = fragmentManager.findFragmentByTag(fragmentTag)

            if (existingFragment == null || existingFragment.isDetached) {
                activity.replaceFragment(
                    R.id.compose_inner_fragment_container,
                    fragment,
                    fragmentTag
                )
            } else if (existingFragment != fragment) {
                activity.replaceFragment(
                    R.id.compose_inner_fragment_container,
                    existingFragment,
                    fragmentTag
                )
            }
        }
    )
}