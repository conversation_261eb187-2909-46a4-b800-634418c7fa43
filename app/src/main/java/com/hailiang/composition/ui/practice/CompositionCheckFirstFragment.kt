package com.hailiang.composition.ui.practice

import android.os.Bundle
import android.view.View
import android.widget.FrameLayout
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.widget.CompositionCheckFirstLayout
import com.hailiang.composition.ui.widget.CompositionStudentImage
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.id
import com.hailiang.xxb.composition.R


/**
 * Description: 查看首次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
class CompositionCheckFirstFragment : BaseFragment(R.layout.fragment_composition_check_first) {
    private val imageLayout: FrameLayout by id(R.id.composition_student_images_container)
    private val contentLayout: FrameLayout by id(R.id.composition_ai_content_container)
    private val compositionViewModel by activityViewModels<CompositionViewModel>()
    private val compositionCheckViewModel by activityViewModels<CompositionCheckViewModel>()

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        contentLayout.clipOutline(15.dp)
        // 加载学生图片
        compositionViewModel.compositionContentState.launchAndCollect(viewLifecycleOwner) {
            it.workDetail?.imgInfoList?.let { imgInfoList ->
                compositionViewModel.combineStudentAnswerImages(imgInfoList.map { imgInfo -> imgInfo.answerPicUrl })
            }
        }
        imageLayout.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val studentAnswerBitmap by compositionViewModel.studentAnswerBitmapState.collectAsStateWithLifecycle()
                    val aiCorrectState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                    val compositionQuestionImagesBitmap by compositionViewModel.compositionQuestionImagesState.collectAsStateWithLifecycle()
                    CompositionStudentImage(
                        aiResponseState = aiCorrectState,
                        bitmapState = studentAnswerBitmap,
                        questionsBitmap = compositionQuestionImagesBitmap,
                    )
                }
            }
        )
        contentLayout.addView(
            ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val aiCorrectState by compositionCheckViewModel.aiFirstCheckDetailState.collectAsStateWithLifecycle()
                    val studentFeedbackState by compositionViewModel.studentFeedbackState.collectAsStateWithLifecycle()
                    CompositionCheckFirstLayout(
                        aiResponseState = aiCorrectState,
                        studentFeedbackState = studentFeedbackState,
                        switchAiTab = {
                            compositionViewModel.reportAiTabSwitchEvent(
                                CompositionStep.CheckFirstPractice,
                                aiSector = it
                            )
                            compositionCheckViewModel.switchFirstAiTab(
                                compositionStep = CompositionStep.CheckFirstPractice,
                                aiSector = it
                            )
                        },
                        retry = {},
                        reload = {},
                        feedbackClick = { aiSector, evaluateFeedback, selected ->
                            compositionViewModel.firstDraftFeedback(
                                aiSector,
                                evaluateFeedback,
                                selected
                            )
                        }
                    )
                }
            }
        )
        compositionCheckViewModel.loadFirstCheckDetail(
            workId = compositionViewModel.curWorkId,
            workStateId = compositionViewModel.curWorkStateId
        )
    }
}