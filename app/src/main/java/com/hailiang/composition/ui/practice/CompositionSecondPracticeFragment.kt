package com.hailiang.composition.ui.practice

import android.graphics.Color
import android.os.Bundle
import android.os.Handler
import android.os.Looper
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.view.ViewTreeObserver
import androidx.compose.runtime.collectAsState
import androidx.compose.runtime.getValue
import androidx.compose.ui.platform.ComposeView
import androidx.compose.ui.platform.ViewCompositionStrategy
import androidx.core.view.ViewCompat
import androidx.core.view.WindowInsetsCompat
import androidx.fragment.app.activityViewModels
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.lifecycle.lifecycleScope
import com.hailiang.common.base.BaseFragment
import com.hailiang.composition.ui.widget.AiCorrectDragView
import com.hailiang.composition.ui.widget.CompositionAiResultWindow
import com.hailiang.composition.ui.widget.CompositionPraiseWidget
import com.hailiang.composition.ui.widget.CompositionTableWidget
import com.hailiang.composition.ui.widget.SubmitSuccessDialog
import com.hailiang.core.ext.launchAndCollect
import com.hailiang.core.ext.launchWithException
import com.hailiang.core.ext.setSingleClickListener
import com.hailiang.hlutil.dp
import com.hailiang.hlutil.ext.clipOutline
import com.hailiang.hlutil.ext.visible
import com.hailiang.composition.dialog.CommonDialog
import com.hailiang.composition.ui.widget.SubmitSuccessWindow
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.textcorrection.dl.TextCorrectionManager
import com.hailiang.ui.designsystem.toast.ToastUtils
import com.hailiang.view.question.composition.CompositionIndex
import com.hailiang.view.question.composition.CompositionTableLayout
import com.hailiang.view.question.composition.InputMethodHelper
import com.hailiang.view.question.composition.bean.CompositionCorrectInfo
import com.hailiang.xxb.composition.R
import com.hailiang.xxb.composition.databinding.FragmentCompositionSecondPracticeBinding
import kotlinx.coroutines.Job
import kotlinx.coroutines.delay
import kotlinx.coroutines.launch
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.isActive
import kotlinx.coroutines.runInterruptible
import kotlinx.coroutines.withContext
import kotlin.collections.sortedBy

/**
 * Description: 二次作答
 *
 * <AUTHOR>
 * @version 2025/2/21 10:58
 */
open class CompositionSecondPracticeFragment :
    BaseFragment(R.layout.fragment_composition_second_practice) {

    private var _binding: FragmentCompositionSecondPracticeBinding? = null
    private val binding get() = _binding!!

    private val practiceViewModel by activityViewModels<CompositionPracticeViewModel>()
    private val compositionViewModel by activityViewModels<CompositionViewModel>()

    private var correctionJob: Job? = null
    private var allCorrectionList: MutableList<CompositionCorrectInfo> = mutableListOf()


    private val compositionWatcher = object : CompositionTableLayout.TextWatcher {
        override fun onTextChanged(
            title: String?,
            content: String?,
            returnRangeList: List<CompositionIndex.ReturnRange>,
        ) {
            practiceViewModel.saveCorrectContent(
                workId = compositionViewModel.curWorkId,
                workStateId = compositionViewModel.curWorkStateId,
                title = title,
                content = content,
                returnRangeList = returnRangeList
            )
            updateTextCount(title, content)
            startCorrectionJob(content, returnRangeList)

        }

        override fun onTextInitFinish(
            title: String?,
            content: String?,
            returnRangeList: List<CompositionIndex.ReturnRange>,
        ) {
            updateTextCount(
                title,
                content
            )
            startCorrectionJob(content, returnRangeList, true)
        }
    }

    fun startCorrectionJob(
        content: String?,
        returnRangeList: List<CompositionIndex.ReturnRange>,
        isInit: Boolean = false
    ) {
        if (TextCorrectionManager.isNotReady()) {
            HLog.e(TAG, "纠错功能暂未初始化完成")
            return
        }
        correctionJob?.cancel()
        correctionJob = lifecycleScope.launch {
            if (!isInit) {
                delay(2_000L)
            }
            val paragraphs = getParagraphs(content, returnRangeList)
            allCorrectionList.clear()
            withContext(Dispatchers.Default) {
                paragraphs.forEachIndexed { index, paragraph ->
                    // 检查取消状态
                    if (!isActive) return@withContext
                    runInterruptible { // 允许线程中断
                        TextCorrectionManager.correctText(paragraph.trim().replace("…", "."))
                    }.also { result ->
                        withContext(Dispatchers.Main) {
                            handleCorrectResult(
                                returnRangeList.get(index).end,
                                content ?: "",
                                result,
                                index == (paragraphs.size - 1)
                            )
                        }
                    }
                }
            }
        }
    }

    private fun getParagraphs(
        content: String?,
        returnRangeList: List<CompositionIndex.ReturnRange>
    ): List<String> {
        content ?: return emptyList()

        val result = mutableListOf<String>()
        var prevEnd = 0

        for ((index, range) in returnRangeList.withIndex()) {
            val start = range.start
            val end = range.end

            // 添加上一个段落到当前换行符之间的内容
            if (start > prevEnd) {
                result.add(content.substring(prevEnd + 1, start))
            } else {
                result.add("") // 空段落
            }

            prevEnd = end
        }

        if (prevEnd < content.length) {
            result.add(content.substring(prevEnd))
        } else if (returnRangeList.isNotEmpty()) {
            result.add("") // 最后一个换行符后面是空段落
        }

        if (result.isNotEmpty() && result[0].isEmpty()) {
            result.removeAt(0)
        }

        return result
    }

//    private fun


    private fun handleCorrectResult(
        startIndex: Int,
        content: String,
        result: TextCorrectionManager.CorrectionResult,
        isLast: Boolean = false
    ) {

        when (result) {
            is TextCorrectionManager.CorrectionResult.Success -> {
                HLog.i(HTag.TAG, "第${startIndex + 1} 纠错结果：${result.message}")
                // 处理纠错结果
                if (result.message != "未发现需要修改的错误") {
//                    HLog.i(HTag.TAG, "第${index + 1}段纠错结果：${result.message}")
                    var changeList: MutableList<CompositionCorrectInfo> =
                        result.corrections.mapNotNull {
                            HLog.i(HTag.TAG, "key:${it.key},value:${it.value}")
                            val changes = it.value.split(",")
                            val index = changes.getOrNull(3)?.toInt()
                            index?.let { idx ->
                                // 明确返回Pair对象
                                changes.getOrNull(1)?.let { correction ->
                                    CompositionCorrectInfo(
                                        start = startIndex + idx,
                                        end = startIndex + idx + 1,
                                        correctText = correction,
                                        colorInt = Color.RED,
                                    )
                                }
                            }
                        }.toMutableList()

                    val mergedChanges = changeList
                        .sortedBy { it.start }
                        .fold(mutableListOf<CompositionCorrectInfo>()) { acc, current ->
                            if (acc.isEmpty()) {
                                acc.add(current)
                            } else {
                                val last = acc.last()
                                if (current.start == last.end) {
                                    // 连续的更改，合并
                                    acc[acc.lastIndex] = CompositionCorrectInfo(
                                        start = last.start,
                                        end = current.end,
                                        correctText = last.correctText + current.correctText,
                                        colorInt = Color.RED,
                                    )
                                } else {
                                    // 不连续的更改，添加新的
                                    acc.add(current)
                                }
                            }
                            acc
                        }
                    allCorrectionList.addAll(mergedChanges)
                }
            }

            is TextCorrectionManager.CorrectionResult.Error -> {
                HLog.e(HTag.TAG, "第${startIndex + 1} 纠错失败：${result.message}")
            }

        }
        if (isLast) {
            if (_binding == null) return
            val compositionTableLayout =
                binding.compositionStudentContentContainer.findViewById<CompositionTableLayout>(
                    R.id.composition_table_layout
                )
            compositionTableLayout.updateCorrectInfo(allCorrectionList)
        }
    }

    private var compositionAiResultWindow: CompositionAiResultWindow? = null

    private var preImeVisible = false
    private val keyboardListener = ViewTreeObserver.OnGlobalLayoutListener {
        try {
            val imeVisible = ViewCompat.getRootWindowInsets(requireActivity().window.decorView)
                ?.isVisible(WindowInsetsCompat.Type.ime()) == true
            binding.compositionSubmitBtn.visible(!imeVisible)

            if (!imeVisible) {
                dismissAiWindow()
            }
            if (imeVisible && preImeVisible != imeVisible) {
                compositionAiResultWindow = CompositionAiResultWindow(requireContext())
                compositionAiResultWindow?.show(compositionViewModel)
            }
            preImeVisible = imeVisible
            compositionViewModel.isShowKeyBoard.value = !imeVisible
        } catch (e: Throwable) {
            //
            dismissAiWindow()
        }
    }

    private fun dismissAiWindow() {
        compositionAiResultWindow?.dismiss()
        compositionAiResultWindow = null
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?,
    ): View {
        _binding = FragmentCompositionSecondPracticeBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        binding.llCompositionStudentContentContainer.clipOutline(20.dp)


        // 加载学生图片资源
        compositionViewModel.studentAnswerImages.launchAndCollect(viewLifecycleOwner) {
            compositionViewModel.downloadAndCombineStudentAnswers(it.imageResources)
        }
        //
        binding.compositionStudentImagesContainer.let {
            it.removeAllViews()
            it.addView(
                ComposeView(requireActivity()).apply {
                    setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                    setContent {
                        val studentAnswerBitmap by compositionViewModel.studentAnswerBitmapState.collectAsStateWithLifecycle()
                        val aiResponseState by compositionViewModel.aiResponseState.collectAsStateWithLifecycle()
                        val studentFeedbackState by compositionViewModel.studentFeedbackState.collectAsStateWithLifecycle()
                        AiCorrectDragView(
                            aiResponseState = aiResponseState,
                            bitmapState = studentAnswerBitmap,
                            defaultHeight = practiceViewModel.cachedDragTopOffset,
                            refreshHeight = {
                                practiceViewModel.saveDragTopOffset(it)
                            },
                            switchAiTab = { sector ->
                                compositionViewModel.switchAiTab(
                                    compositionStep = CompositionStep.SecondPractice,
                                    aiSector = sector
                                )
                            },
                            onBack = {
                                dismissAiWindow()
                                compositionViewModel.reportAiTabSwitchEvent()
                                compositionViewModel.toAiGuidance()
                            },
                            reload = compositionViewModel::reloadAiStream,
                            footer = {
//                                CompositionPraiseWidget(
//                                    aiResponseState = aiResponseState,
//                                    initFeedback = studentFeedbackState,
//                                    feedbackClick = { aiSector, evaluateFeedback, selected ->
//                                        compositionViewModel.firstDraftFeedback(
//                                            aiSector,
//                                            evaluateFeedback,
//                                            selected
//                                        )
//                                    },
//                                )
                            },
                            initFeedback = studentFeedbackState,
                            feedbackClick = { aiSector, evaluateFeedback, selected ->
                                compositionViewModel.firstDraftFeedback(
                                    aiSector, evaluateFeedback, selected
                                )
                            },
                        )
                    }
                }
            )
        }
        binding.compositionSubmitBtn.setSingleClickListener { // 提交
            dismissAiWindow()
            practiceViewModel.secondSubmit(
                workId = compositionViewModel.curWorkId,
                workStateId = compositionViewModel.curWorkStateId
            ) { enable, message, submitFunc ->
                if (enable) {
                    CommonDialog.Builder()
                        .setTitle("提示")
                        .setContentText("提交后无法再修改，确定提交？")
                        .setPositiveAction(text = "提交") {
                            submitFunc?.invoke()
                        }.show(childFragmentManager)
                } else {
                    ToastUtils.showShort(message)
                }
            }
        }
        // 作文表格
        binding.compositionStudentContentContainer.let {
            it.removeAllViews()
            it.addView(ComposeView(requireActivity()).apply {
                setViewCompositionStrategy(ViewCompositionStrategy.DisposeOnViewTreeLifecycleDestroyed)
                setContent {
                    val compositionOcrState by compositionViewModel.compositionTableState.collectAsState()
                    when (compositionOcrState) {
                        is CompositionTableState.Success -> {
                            CompositionTableWidget(
                                composition = (compositionOcrState as CompositionTableState.Success).composition,
                                editable = true,
                                showBottomPadding = true,
                                textWatcher = compositionWatcher
                            )
                        }

                        else -> {
                            CompositionTableWidget(composition = null)
                        }
                    }
                }
            })
        }
        practiceViewModel.showSubmitTipView.launchAndCollect(viewLifecycleOwner) {
            compositionViewModel.markSubmitSuccess()
            val submitSuccessWindow = SubmitSuccessWindow(requireContext())
            submitSuccessWindow.show()
            compositionViewModel.reloadAiStream()
        }
        // ----------------------------------------------------------------------
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(keyboardListener)
        binding.root.viewTreeObserver.addOnGlobalLayoutListener(keyboardListener)
    }

    private fun updateTextCount(title: String?, content: String?) {
        lifecycleScope.launchWithException {
            // 排除空格
            val count = "${title}${content}".replace(" ", "").count()
            binding.compositionWordCountTv.text = "共${count}字"
            binding.compositionWordCountIv.visible(true)
            binding.compositionSaveTipTv.visible(true)

        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
//        ViewCompat.setOnApplyWindowInsetsListener(binding.root, null)
        binding.root.viewTreeObserver.removeOnGlobalLayoutListener(keyboardListener)
        InputMethodHelper.dismiss(requireContext())
        _binding = null
    }
}