package com.hailiang.composition.ui.practice

import com.hailiang.composition.data.bean.AiStreamType
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.bean.WorkStatus
import com.hailiang.composition.data.repository.WorkReposition
import com.hailiang.composition.ui.practice.AiWorkFlowManager.AiFlow
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.workcloud.data.vo.DoWorkStateInfo

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/2/21 11:02
 */
class CompositionStepManager(
    private val workDetail: WorkDetail,
) {
    private val workReposition = WorkReposition()
    private val workId: Long = workDetail.schoolworkInfo?.id ?: -1L
    private val workStateId: Long = workDetail.schoolworkStateInfo?.id ?: -1L

    var currentStep: CompositionStep
        private set

    private val workState: DoWorkStateInfo
    private val currentWorkState get() = workState.state

    private var aiStatus: AiFlow = AiFlow.Default

    init {
        workState = workReposition.queryWorkState(workId = workId, workStateId = workStateId)
            ?: DoWorkStateInfo.obtainByWorkDetail(workDetail)
        currentStep = loadDefaultStep()
    }

    fun updateStepWhenAiStatusChanged(newAiStatus: AiFlow): CompositionStep {
        val preAisStatus = aiStatus
        this.aiStatus = newAiStatus
        when {
            currentStep == CompositionStep.Default || preAisStatus == AiFlow.Default -> {
                currentStep = switchStep(loadDefaultStep())
            }

            preAisStatus is AiFlow.OcrFailed || preAisStatus is AiFlow.StreamPreparing -> {
                currentStep = switchStep(loadDefaultStep())
            }

            else -> {}
        }
        return currentStep
    }

    private fun loadDefaultStep(): CompositionStep {
        if (workId <= 0 || aiStatus == AiFlow.Default) {
            return CompositionStep.Default
        }
        val state = workState.state
        return when {
            aiStatus is AiFlow.TitleOrcFailed || aiStatus is AiFlow.TitleOcrPreparing -> {
//                return CompositionStep.FirstPractice
                return CompositionStep.AiGuidance // 此时需要这一步 默认跳转到 AiGuidance
            }

            aiStatus is AiFlow.OcrFailed || aiStatus is AiFlow.ContentOcrPreparing -> {
                return CompositionStep.AiGuidance
            }

            aiStatus is AiFlow.StreamPreparing ->{
                when((aiStatus as  AiFlow.StreamPreparing).streamType){
                    AiStreamType.Second ->{
                        if (isAllSubmitted()){
                            return CompositionStep.CheckSecondPractice
                        }else{
                            return CompositionStep.AiGuidance
                        }
                    }
                    else -> {
                        return CompositionStep.AiGuidance
                    }
                }
            }

            aiStatus  is AiFlow.Streaming->{
                when((aiStatus as  AiFlow.Streaming).streamType){
                    AiStreamType.Second ->{
                        return CompositionStep.CheckSecondPractice
                    }
                    else -> {
                        return CompositionStep.AiGuidance
                    }
                }
            }

            state <= WorkStatus.DOING -> {
//                return CompositionStep.FirstPractice
                return CompositionStep.AiGuidance
            }

            state == WorkStatus.DONE -> { // 提交后需要停留在 上传题目，OCR成功后再切换到AI
                when {
                    aiStatus == AiFlow.Default -> {
                        return CompositionStep.Default
                    }

                    aiStatus.topicPrepared() -> {
                        return CompositionStep.AiGuidance
                    }

                    else -> {
//                        return CompositionStep.FirstPractice
                        return CompositionStep.AiGuidance
                    }
                }
            }

            state == WorkStatus.SELF_CORRECT -> {
                return CompositionStep.SecondPractice
            }

            else -> { // 其他状态都是默认打开检查
                return CompositionStep.CheckSecondPractice
            }
        }.apply {
            HLog.i(
                HTag.TAG,
                "初始化步骤信息完成: workId: $workId, workStateId: $workStateId;aiStatus：$aiStatus, currentStep: $this"
            )
        }
    }

    fun switchStep(newStep: CompositionStep): CompositionStep {
        if (workId <= 0 || aiStatus == AiFlow.Default) { // 没有数据的情况，返回默认
            return CompositionStep.Default
        }
        return if (isAllSubmitted()) { // 全部已提交，去查看题目页面
            switchCheckStep(newStep)
        } else {
            switchPracticeStep(newStep)
        }.apply {
            currentStep = this
        }
    }

    /**
     * 切换做题时的步骤
     */
    private fun switchPracticeStep(newStep: CompositionStep): CompositionStep {
//        if (newStep == currentStep || (newStep == CompositionStep.AiGuidance && workState.state < WorkStatus.DONE)) {
//            // 作业未完前不能调整到 做题后的步骤
////            HLog.i(HTag.TAG, "切换到步骤: $newStep")
//            return currentStep
//        }
        if (newStep == currentStep) {
            HLog.i(HTag.TAG, "切换到步骤: $newStep")
            return currentStep
        }
        // 切换到首次做题，且是未做题状态，更新为做题中
        if (newStep is CompositionStep.FirstPractice) {
            if (currentWorkState == WorkStatus.UN_DO) {
                updateWorkState(WorkStatus.DOING)
            }
            // 仅 WorkStatus.DONE 之前可以编辑
            newStep.editable = currentWorkState < WorkStatus.DONE
        }
        // 最大激活步骤，表示这个步骤之前都能切换
//        val maxActiveStep = if (currentWorkState >= WorkStatus.DONE && aiStatus.topicPrepared()) {
//            CompositionStep.AiGuidance
//        } else {
//            CompositionStep.FirstPractice
//        }
        val maxActiveStep = CompositionStep.AiGuidance
        newStep.activatedStep = maxActiveStep
        HLog.i(HTag.TAG, "切换到步骤: $newStep")
        return newStep
    }

    /**
     * 切换查看作业时的步骤
     */
    private fun switchCheckStep(step: CompositionStep): CompositionStep {
//        HLog.i(HTag.TAG, "切换查看作业时的步骤: $step")
        return if (step.step < CompositionStep.CheckQuestion.step) {
            CompositionStep.CheckSecondPractice
        } else {
            step
        }.apply {
            this.activatedStep = CompositionStep.CheckSecondPractice
        }
    }

    fun addTimeCounting(offset: Long) {
        workState.timeCounting += offset
        workReposition.updateWorkState(workState)
    }

    fun updateWorkState(newState: Int) {
        workState.state = newState
        workReposition.updateWorkState(workState)
    }

    fun isAllSubmitted(): Boolean {
        return currentWorkState >= WorkStatus.ALL_SUBMITTED
    }

}