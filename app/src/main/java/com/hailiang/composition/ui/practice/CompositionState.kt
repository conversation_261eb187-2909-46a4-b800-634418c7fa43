package com.hailiang.composition.ui.practice

import android.graphics.Bitmap
import androidx.annotation.IntRange
import com.hailiang.common.download.resource.WorkResource
import com.hailiang.composition.data.bean.AiStreamDetail
import com.hailiang.composition.data.bean.CompositionCheckBean.Advice
import com.hailiang.composition.data.bean.CompositionCheckBean.Allusion
import com.hailiang.composition.data.bean.CompositionCheckBean.ComprehensiveJudge
import com.hailiang.composition.data.bean.CompositionStatusResponse
import com.hailiang.composition.data.bean.WorkDetail
import com.hailiang.composition.data.enums.JobStatus
import com.hailiang.composition.mediatools.MediaImage
import com.hailiang.view.question.composition.Composition
import java.util.UUID

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/3/16 02:23
 */
/**
 * 作业题目、材料等信息
 */
data class CompositionContentState(
    val workId: Long = -1L,
    val workStateId: Long = -1L,
    val workDetail: WorkDetail? = null,
)


/**
 * 题目图片Bitmap
 */
sealed interface CompositionQuestionImageState {
    data class Success(
        val combineImage: Bitmap?,
    ) : CompositionQuestionImageState

    data object Loading : CompositionQuestionImageState
}

///**
// * 学生答案图片资源
// */
data class CompositionStudentAnswersState(
    val imageResources: List<WorkResource>? = null,
    val mediaImageList: List<MediaImage>? = null,
)

/**
 * 学生答案图片Bitmap
 */
sealed interface CompositionStudentAnswersBitmapState {
    data class Success(
        val combineImage: Bitmap?,
    ) : CompositionStudentAnswersBitmapState

    data object Loading : CompositionStudentAnswersBitmapState
}


/**
 * 作文表格状态
 */
sealed class CompositionTableState {
    data object Loading :
        CompositionTableState()

    data class Success(
        val ocrTitle: String?,
        val ocrContent: String?,
        val composition: Composition?,
        val needDoCompare: Boolean,
    ) : CompositionTableState()
}

/**
 * Ai 批改状态
 */
sealed interface AiResponseState {
    data object Default : AiResponseState

    data object Loading : AiResponseState

    data class OcrLoading(@IntRange(0, 100) val progress: Int, val isTopic: Boolean) :
        AiResponseState

    /**
     * 思考、回答中
     */
    data class AiStreaming(val aiStreamDetail: AiStreamDetail) : AiResponseState

    /**
     *  流失败，要重试，触发服务端重试
     */
    data class AiStreamError(val aiStreamDetail: AiStreamDetail) : AiResponseState

    /**
     * 请求失败，要重新请求
     */
    data class AiRequestError(val aiStreamDetail: AiStreamDetail) : AiResponseState

    //
    /**
     * 标题OCR失败
     */
    data class TitleOcrFailed(
        val id: String = UUID.randomUUID().toString(),
        val compositionStatusResponse: CompositionStatusResponse?,  // 包含ocrResult
        val errorMessage: String?,
    ) : AiResponseState

    /**
     * 内容OCR 失败
     */
    data class ContentOcrFailed(
        val id: String = UUID.randomUUID().toString(),
        val errorMessage: String?,
    ) : AiResponseState

    /**
     * AI重试
     * 会调用重试接口，触发服务端重试
     */
    object Retry : AiResponseState

    /**
     * 重新加载，直接调用接口重新获取数据，不触发服务端重试
     */
    data object Reload : AiResponseState

    data class Success(
        val allJobStatus: JobStatus,
        val scoreJobStatus: JobStatus = JobStatus.SUCCESS,
        val allusionJobStatus: JobStatus = JobStatus.SUCCESS,
        val selectedAiSector: AiSector,
        val aiSubSectorList: List<AiSubSector>,
        /**
         * 综合评价pp
         */
        val comprehensiveJudge: ComprehensiveJudge?,
        /**
         * 点拨
         */
        val adviceList: List<Advice>?,
        /**
         * 典故列表（知识加油站）
         */
        val allusionList: List<Allusion>?,
        val score: Int,
    ) : AiResponseState {
        val selectedJobStatus
            get() = when (selectedAiSector) {
                AiSector.Evaluate -> allJobStatus
                AiSector.Dibble -> allJobStatus
                AiSector.Petrol -> allusionJobStatus
            }
    }
}

data class EvaluateFeedbackState(
    val feedbackMap: Map<AiSector, EvaluateFeedback?>,
    val needAnimation: Boolean,
)