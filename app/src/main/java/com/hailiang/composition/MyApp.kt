package com.hailiang.composition

import com.hailiang.common.base.XxbApplication
import com.hailiang.common.util.ActivityManager
import com.hailiang.core.base.AbsLogActivity

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/6/9 19:21
 */
class MyApp : XxbApplication() {

    override fun onCreate() {
        super.onCreate()
//        AbsLogActivity.globalLog = true
        registerActivityLifecycleCallbacks(ActivityManager(this))
    }
}