package com.hailiang.composition.util

import com.hailiang.hlutil.SharedPrefUtil
import com.hailiang.hlutil.getApplication
import com.hailiang.opensdk.LauncherData

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/16 19:37
 */
object SpManager {

    private val sp
        get() = SharedPrefUtil.newInstance(
            getApplication(),
            "config"
        )

    /**
     * 拍照引导是否已读
     */
    fun isTakePhotoBeginnerGuidanceRead(): Boolean {
        return sp["take_photo_beginner_guidance_${LauncherData.getUserId()}", false]
    }

    /**
     * 标记拍照引导是否已读
     */
    fun markTakePhotoBeginnerGuidanceRead() {
        sp.put("take_photo_beginner_guidance_${LauncherData.getUserId()}", true).apply()
    }

    fun getTextCorrectionInfo():String{
        return sp["text_correction_info", ""]
    }

    fun saveTextCorrectionInfo(info:String){
        sp.put("text_correction_info", info).apply()
    }
}