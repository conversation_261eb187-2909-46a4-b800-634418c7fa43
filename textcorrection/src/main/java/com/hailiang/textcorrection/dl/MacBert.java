package com.hailiang.textcorrection.dl;


import com.hailiang.textcorrection.dl.bert.LoadModel;
import com.hailiang.textcorrection.dl.bert.tokenizerimpl.BertTokenizer;
import com.hailiang.textcorrection.dl.bert.utils.CollectionUtil;

import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

import ai.onnxruntime.OnnxTensor;
import ai.onnxruntime.OnnxValue;
import ai.onnxruntime.OrtEnvironment;
import ai.onnxruntime.OrtException;
import ai.onnxruntime.OrtSession;
import ai.onnxruntime.OrtUtil;


/**
 * <AUTHOR>
 * @date 2022/5/31 19:03
 */
public class MacBert {

    private final BertTokenizer tokenizer;

    public MacBert(String vocab_file) {
        tokenizer = new BertTokenizer(vocab_file);
    }

    /**
     * tokenize text
     *
     * @param text
     * @return
     * @throws Exception
     */
    public Pair<BertTokenizer, Map<String, OnnxTensor>> parseInputText(String text) throws Exception {
        OrtEnvironment env = LoadModel.env;
        List<String> tokens = tokenizer.tokenize(text);
        List<Integer> tokenIds = tokenizer.convert_tokens_to_ids(tokens);
        long[] inputIds = new long[tokenIds.size()];
        long[] attentionMask = new long[tokenIds.size()];
        long[] tokenTypeIds = new long[tokenIds.size()];
        for (int index = 0; index < tokenIds.size(); index++) {
            inputIds[index] = tokenIds.get(index);
            attentionMask[index] = 1;
            tokenTypeIds[index] = 0;
        }
        long[] shape = new long[]{1, inputIds.length};
        Object ObjInputIds = OrtUtil.reshape(inputIds, shape);
        Object ObjAttentionMask = OrtUtil.reshape(attentionMask, shape);
        Object ObjTokenTypeIds = OrtUtil.reshape(tokenTypeIds, shape);
        OnnxTensor input_ids = OnnxTensor.createTensor(env, ObjInputIds);
        OnnxTensor attention_mask = OnnxTensor.createTensor(env, ObjAttentionMask);
        OnnxTensor token_type_ids = OnnxTensor.createTensor(env, ObjTokenTypeIds);
//        Map inputs = Map.of("input_ids", input_ids, "attention_mask", attention_mask, "token_type_ids",
//        token_type_ids);

        Map<String, OnnxTensor> inputs = new HashMap<>();
        inputs.put("input_ids", input_ids);
        inputs.put("attention_mask", attention_mask);
        inputs.put("token_type_ids", token_type_ids);
        return Pair.of(tokenizer, inputs);
    }

    /**
     * correct text
     *
     * @param triple
     * @return
     */
    public List<String> predCSC(Pair<BertTokenizer, Map<String, OnnxTensor>> triple) {
        BertTokenizer tokenizer = triple.getLeft();
        Map<String, OnnxTensor> inputs = triple.getRight();
        List<String> predTokenList = CollectionUtil.newArrayList();
        try {
            OrtSession session = LoadModel.session;
            try (OrtSession.Result results = session.run(inputs)) {
                OnnxValue onnxValue = results.get(0);
                float[][][] labels = (float[][][]) onnxValue.getValue();
                // 直接在原始数组上处理，避免使用 Nd4j
                int[] predIndex = new int[labels[0].length];
                for (int i = 0; i < labels[0].length; i++) {
                    float max = Float.NEGATIVE_INFINITY;
                    int maxIndex = 0;
                    for (int j = 0; j < labels[0][i].length; j++) {
                        if (labels[0][i][j] > max) {
                            max = labels[0][i][j];
                            maxIndex = j;
                        }
                    }
                    predIndex[i] = maxIndex;
                }
                for (int idx = 1; idx < predIndex.length - 1; idx++) {
                    predTokenList.add(tokenizer.convert_ids_to_tokens(predIndex[idx]));
                }
            }
        } catch (OrtException e) {
            e.printStackTrace();
        }
        return predTokenList;
    }

    public List<Pair<String, String>> getErrors(String correctedText, String originText) {
        List<String> specialList = CollectionUtil.newArrayList();
        specialList.add(" ");
        specialList.add("“");
        specialList.add("”");
        specialList.add("‘");
        specialList.add("’");
        specialList.add("琊");
        specialList.add("\n");
        specialList.add("…");
        specialList.add("—");
        specialList.add("擤");
        List<Pair<String, String>> subDetails = CollectionUtil.newArrayList();
        int minLength = Math.min(correctedText.length(), originText.length());

        for (int i = 0; i < minLength; i++) {
            String oriChar = originText.substring(i, i + 1);
            if (specialList.contains(oriChar)) {
                // add unk word
                correctedText = correctedText.substring(0, i) + oriChar + correctedText.substring(i + 1);
                continue;
            }
//            if (i >= correctedText.length()) {
//                continue;
//            }
            String corChar = correctedText.substring(i, i + 1);
            if (!StringUtils.equals(oriChar, corChar)) {
                if (oriChar.equalsIgnoreCase(corChar)) {
                    // pass english upper char
                    correctedText = correctedText.substring(0, i) + oriChar + correctedText.substring(i + 1);
                    continue;
                }
                StringBuffer sb = new StringBuffer();
                sb.append(oriChar).append(",").append(correctedText, i, i + 1).append(",").append(i).append(",").append(i + 1);
                subDetails.add(Pair.of(correctedText, sb.toString()));
            }
        }
        // 处理 originText 比 correctedText 长的情况
//        if (originText.length() > minLength) {
//            for (int i = minLength; i < originText.length(); i++) {
//                String oriChar = originText.substring(i, i + 1);
//                if (!specialList.contains(oriChar)) {
//                    StringBuffer sb = new StringBuffer();
//                    sb.append(oriChar).append(",").append("").append(",").append(i).append(",").append(i + 1);
//                    subDetails.add(Pair.of(correctedText, sb.toString()));
//                }
//            }
//        }
        return subDetails;
    }

}

