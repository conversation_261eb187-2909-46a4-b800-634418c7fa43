package com.hailiang.textcorrection.dl

import android.content.Context
import android.util.Log
import com.hailiang.common.download.resource.WorkResource
import com.hailiang.common.download.resource.WorkResourceDownload
import com.hailiang.core.thread.ThreadPlugins
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.hlutil.compress.ZipUtil
import com.hailiang.textcorrection.dl.bert.LoadModel
import com.hailiang.workcloud.download.resource.ResourceDownloadCallback
import org.apache.commons.lang3.StringUtils
import java.io.File
import java.util.stream.Collectors
import org.apache.commons.lang3.tuple.Pair;


object TextCorrectionManager {

    private var macBert: MacBert? = null
    private const val modelPath = "pycorrector/onnx"
    var state: State = State.NOT_INITIALIZED

    enum class State {
        NOT_INITIALIZED, DOWNLOADING, EXTRACTING, INITIALIZING, INITIALIZED, FAILED, RETRY
    }

    fun initialize(
        context: Context,
        url: String,
        callbackState: (state: State) -> Unit = {},
        retry: Boolean = false
    ) {
        state = State.INITIALIZING
        callbackState.invoke(state)
        try {
            loadResource(
                context = context,
                url = url,
                callbackState = callbackState,
                onFinishDownload = {
                    System.setProperty("org.bytedeco.javacpp.maxphysicalbytes", "256M")
                    System.setProperty("org.bytedeco.javacpp.maxbytes", "128M")
                    initOnnxRuntime(context)
                    state = State.INITIALIZED
                    callbackState.invoke(state)
                },
                onFail = { _, _ ->
                    state = State.FAILED
                    callbackState.invoke(state)
                    if (retry) {
                        retry(context = context, url = url, callbackState = callbackState)
                    }
                })
        } catch (e: Exception) {
            //如果初始化失败，重新下载 解压
            state = State.FAILED
            callbackState.invoke(state)
            Log.e("TextCorrection", "初始化失败", e)
            if (retry) {
                retry(context = context, url = url, callbackState = callbackState)
            }
        }
    }

    fun retry(context: Context, url: String, callbackState: (state: State) -> Unit = {}) {
        state = State.RETRY
        callbackState.invoke(state)
        val modelDir = File(context.filesDir, modelPath)
        if (modelDir.exists()) {
            modelDir.deleteRecursively()
        }
        initialize(context = context, url = url, callbackState = callbackState, retry = true)
    }

    fun correctText(text: String): CorrectionResult {
        if (state != State.INITIALIZED) {
            return CorrectionResult.Error("模型未初始化完成，当前状态: ${state.name}")
        }
        if (text.isBlank()) {
            return CorrectionResult.Error("请输入要检查的文本")
        }
        return try {
            val lowerCaseText = StringUtils.lowerCase(text)
            val pair = macBert!!.parseInputText(lowerCaseText)
            var predTokenList = macBert!!.predCSC(pair)
            predTokenList = predTokenList!!.stream().map { token: String -> token.replace("##", "") }
                    .collect(Collectors.toList())
            val predString = java.lang.String.join("", predTokenList)
            val resultList = macBert!!.getErrors(predString, lowerCaseText)
            if (resultList.isEmpty()) {
                CorrectionResult.Success(emptyList(), "未发现需要修改的错误")
            } else {
                CorrectionResult.Success(resultList, resultList.joinToString("\n") {
                    "${it.left} => ${it.right}"
                })
            }

        } catch (e: Exception) {
            Log.e("TextCorrection", "纠错失败", e)
            CorrectionResult.Error("纠错错误: ${e.message}")
        }
    }

    fun release() {
        macBert = null
    }

    private fun initOnnxRuntime(context: Context) {
        val modelDir = File(context.filesDir, modelPath)
        try {
            LoadModel.loadOnnxModel(modelDir.resolve("macbertv3.onnx").absolutePath)
            macBert = MacBert(modelDir.resolve("vocab.txt").absolutePath)
        } catch (e: Exception) {
            deleteModel(context)
            state = State.FAILED
            HLog.e(HTag.TAG, "下载解析失败")
        }

    }

    fun deleteModel(context: Context){
        val modelDir = File(context.filesDir, modelPath)
        if (modelDir.exists()) {
            modelDir.deleteRecursively()
        }
    }

    public fun isNotReady(): Boolean {
        if (state == State.NOT_INITIALIZED || state == State.DOWNLOADING || state == State.EXTRACTING) {
            return true
        }
        return false
    }


    private fun loadResource(
        context: Context,
        url: String,
        callbackState: (state: State) -> Unit,
        onFinishDownload: (outPathString: String) -> Unit = {},
        onFail: (workResource: WorkResource, errorInfo: String?) -> Unit = { _, _ -> }
    ) {
        val modelDir = File(context.filesDir, modelPath)
        if (!modelDir.exists()) {
            modelDir.mkdirs()
            val createZipFileResource = WorkResource.createZipFileResource(
                url = url,
            )
            state = State.DOWNLOADING
            callbackState.invoke(state)
            WorkResourceDownload.downloadResources(
                resources = listOf(createZipFileResource),
                downloadListener = object : ResourceDownloadCallback {
                    override fun onStartDownload(workResource: WorkResource) {

                    }

                    override fun onFinishDownload(
                        workResource: WorkResource, savedFile: String?
                    ) {
                        if (savedFile != null) {
                            File(savedFile).exists().let {
                                ThreadPlugins.runOnWorkThread({
                                    state = State.EXTRACTING
                                    callbackState.invoke(state)
                                    ZipUtil.unCompressToFolder(savedFile, modelDir.absolutePath)
                                    ThreadPlugins.runOnMainThread({
                                        onFinishDownload.invoke(modelDir.absolutePath)
                                    })
                                })
                            }
                        }
                    }

                    override fun onFail(
                        workResource: WorkResource, errorInfo: String?
                    ) {
                        onFail.invoke(workResource, errorInfo)
                    }

                    override fun onAllComplete() {
                    }
                })
        } else {
            onFinishDownload.invoke(modelDir.absolutePath)
        }
    }


    sealed class CorrectionResult {
        data class Success(val corrections: List<Pair<String, String>>, val message: String) :
            CorrectionResult()

        data class Error(val message: String) : CorrectionResult()
    }
}