android {
    signingConfigs {
        getByName("debug") {
            storeFile = file("../common/signature/otterdesk.keystore")
            storePassword = "otterdesk"
            keyAlias = "otterdesk"
            keyPassword = "otterdesk"
        }
        create("release") {
            storeFile = file("../common/signature/otterdesk.keystore")
            storePassword = "otterdesk"
            keyAlias = "otterdesk"
            keyPassword = "otterdesk"
        }
    }
    buildTypes {
        debug {
            minifyEnabled false
            proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro"
            )
            signingConfig = signingConfigs.named("debug").get()
        }
        release {
            minifyEnabled false
            proguardFiles(
                    getDefaultProguardFile("proguard-android-optimize.txt"),
                    "proguard-rules.pro"
            )
            signingConfig = signingConfigs.named("release").get()
        }
    }
}

//apk包重命名并移动到outputs目录下
android.applicationVariants.all { variant ->
    variant.outputs.all { output ->
        def outputFile = output.outputFile
        if (outputFile != null && outputFile.name.endsWith('.apk')) {
            outputFileName = "${rootProject.name}_${variant.flavorName}_${variant.buildType.name}_v${APP_VERSION_NAME}@${APP_VERSION_CODE}_${new Date().format('yyyyMMddHHmm')}.apk"
        }
    }
    variant.assemble.doLast {
        delete "${rootProject.projectDir}/outputs"
        copy {
            from variant.outputs*.outputFile
            into "${rootProject.projectDir}/outputs"
        }
    }
}

