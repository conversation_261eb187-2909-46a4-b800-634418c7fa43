[versions]
agp = "8.5.0"
kotlin = "1.9.21"
coreKtx = "1.10.1"
junit = "4.13.2"
junitVersion = "1.1.5"
espressoCore = "3.5.1"
lifecycleRuntimeKtx = "2.6.1"
activityCompose = "1.8.2"
composeBom = "2023.08.00"
compose-compiler = "1.5.7"
camera = "1.3.0-alpha04"

compileSdk = "34"
minSdk = "26"
targetSdk = "29"

androidx-appcompat = "1.3.1"
android-material = "1.2.1"
androidx-recyclerview = "1.3.0"
androidx-runtime-compose = "2.6.1"
androidx-room = "2.5.1"
androidx-constrsint = "2.1.4"
androidx-constrsint-compose = "1.0.1"

hl-core = "1.1.4"
hl-util = "1.0.8"
hl-http = "1.0.8"
hl-adapter = "1.1.0"
hl-autosize = "1.0.4"
hl-bugly = "1.0.8"
hl-component = "1.0.3"
hl-resource = "1.0.0"
hl-sls = "1.1.3"
hl-opensdk = "1.0.4.9"
hl-camera = "1.0.3.1"
hl-refresh = "1.0.0"
hl-markdown = "1.0.7"
hl-question-composition = "1.0.2"
hl-handwrite = "1.0.7"
hl-handwrite-toolbar = "1.0.4.1"

third-okhttp = "4.12.0"
third-okhttp-sse = "4.12.0"
third-glide = "4.16.0"
third-fastjson2 = "2.0.38"
third-leakcanary = "2.10"
third-chuck = "4.1.0"
third-lottie = "5.2.0"
third-blankj = "1.31.1"

kotlinGradlePlugin = "1.9.21"
androidGradlePlugin = "7.4.2"
third-circle-imageview = "3.1.0"

[libraries]
android-gradle-plugin = { group = "com.android.tools.build", name = "gradle", version.ref = "androidGradlePlugin" }
kotlin-gradle-plugin = { module = "org.jetbrains.kotlin:kotlin-gradle-plugin", version.ref = "kotlinGradlePlugin" }

androidx-core-ktx = { group = "androidx.core", name = "core-ktx", version.ref = "coreKtx" }
junit = { group = "junit", name = "junit", version.ref = "junit" }
androidx-junit = { group = "androidx.test.ext", name = "junit", version.ref = "junitVersion" }
androidx-espresso-core = { group = "androidx.test.espresso", name = "espresso-core", version.ref = "espressoCore" }
androidx-lifecycle-runtime-ktx = { group = "androidx.lifecycle", name = "lifecycle-runtime-ktx", version.ref = "lifecycleRuntimeKtx" }
androidx-activity-compose = { group = "androidx.activity", name = "activity-compose", version.ref = "activityCompose" }
androidx-compose-bom = { group = "androidx.compose", name = "compose-bom", version.ref = "composeBom" }
androidx-runtime-compose = { module = "androidx.lifecycle:lifecycle-runtime-compose", version.ref = "androidx-runtime-compose" }
androidx-ui = { group = "androidx.compose.ui", name = "ui" }
androidx-ui-graphics = { group = "androidx.compose.ui", name = "ui-graphics" }
androidx-ui-tooling = { group = "androidx.compose.ui", name = "ui-tooling" }
androidx-ui-tooling-preview = { group = "androidx.compose.ui", name = "ui-tooling-preview" }
androidx-ui-test-manifest = { group = "androidx.compose.ui", name = "ui-test-manifest" }
androidx-ui-test-junit4 = { group = "androidx.compose.ui", name = "ui-test-junit4" }
androidx-material3 = { group = "androidx.compose.material3", name = "material3" }
camera-core = { group = "androidx.camera", name = "camera-core", version.ref = "camera" }
camera-camera2 = { group = "androidx.camera", name = "camera-camera2", version.ref = "camera" }
camera-lifecycle = { group = "androidx.camera", name = "camera-lifecycle", version.ref = "camera" }
camera-extensions = { group = "androidx.camera", name = "camera-extensions", version.ref = "camera" }
camera-view = { group = "androidx.camera", name = "camera-view", version.ref = "camera" }


androidx-appcompat = { module = "androidx.appcompat:appcompat", version.ref = "androidx-appcompat" }
android-material = { module = "com.google.android.material:material", version.ref = "android-material" }
androidx-recyclerview = { module = "androidx.recyclerview:recyclerview", version.ref = "androidx-recyclerview" }
androidx-room = { module = "androidx.room:room-runtime", version.ref = "androidx-room" }
androidx-room-ktx = { module = "androidx.room:room-ktx", version.ref = "androidx-room" }
androidx-room-compiler = { module = "androidx.room:room-compiler", version.ref = "androidx-room" }
androidx-constraint = { module = "androidx.constraintlayout:constraintlayout", version.ref = "androidx-constrsint" }
androidx-constraint-compose = { module = "androidx.constraintlayout:constraintlayout-compose", version.ref = "androidx-constrsint-compose" }

#internal
hailiang-core = { module = "com.hailiang.xxb:core", version.ref = "hl-core" }
hailiang-util = { module = "com.hailiang.xxb:util", version.ref = "hl-util" }
hailiang-http = { module = "com.hailiang.xxb:http", version.ref = "hl-http" }
hailiang-component = { module = "com.hailiang.xxb:component", version.ref = "hl-component" }
hailiang-adapter = { module = "com.hailiang.xxb:adapter", version.ref = "hl-adapter" }
hailiang-autosize = { module = "com.hailiang.ui:autosize", version.ref = "hl-autosize" }
hailiang-bugly = { module = "com.hailiang.xxb:bugly", version.ref = "hl-bugly" }
hailiang-resource = { module = "com.hailiang.xxb:resource", version.ref = "hl-resource" }
hailiang-handwrite = { module = "com.hailiang.xxb:handwrite", version.ref = "hl-handwrite" }
hailiang-handwrite-toolbar = { module = "com.hailiang.ui:handwrite-toolbar", version.ref = "hl-handwrite-toolbar" }
hailiang-sls = { module = "com.hailiang.hlsls:hlsls", version.ref = "hl-sls" }
hailiang-opensdk = { module = "com.hailiang.core:opensdk", version.ref = "hl-opensdk" }
hailiang-camera = { module = "com.hailiang.hlcamera:hlcamera", version.ref = "hl-camera" }
hailiang-refresh = { module = "com.hailiang.xxb:smart-refresh", version.ref = "hl-refresh" }
hailiang-markdown = { module = "com.hailiang.ui:markdown", version.ref = "hl-markdown" }
hailiang-question-composition = { module = "com.hailiang.question:composition", version.ref = "hl-question-composition" }

#third
third-okhttp = { module = "com.squareup.okhttp3:okhttp", version.ref = "third-okhttp" }
third-okhttp-sse = { module = "com.squareup.okhttp3:okhttp-sse", version.ref = "third-okhttp-sse" }
third-glide = { module = "com.github.bumptech.glide:glide", version.ref = "third-glide" }
third-glide-compiler = { module = "com.github.bumptech.glide:compiler", version.ref = "third-glide" }
third-fastjson2 = { module = "com.alibaba.fastjson2:fastjson2", version.ref = "third-fastjson2" }
third-leakcanary = { module = "com.squareup.leakcanary:leakcanary-android", version.ref = "third-leakcanary" }
third-chuck = { module = "com.github.chuckerteam.chucker:library", version.ref = "third-chuck" }
third-lottie = { module = "com.airbnb.android:lottie-compose", version.ref = "third-lottie" }
third-blankj = { module = "com.blankj:utilcodex", version.ref = "third-blankj" }

third-circle-imageview = { module = "de.hdodenhof:circleimageview", version.ref = "third-circle-imageview" }

[plugins]
android-application = { id = "com.android.application", version.ref = "agp" }
kotlin-android = { id = "org.jetbrains.kotlin.android", version.ref = "kotlin" }
kotlin-compose = { id = "org.jetbrains.kotlin.plugin.compose", version.ref = "kotlin" }
android-library = { id = "com.android.library", version.ref = "agp" }
kapt = { id = "org.jetbrains.kotlin.kapt", version.ref = "kotlinGradlePlugin" }

