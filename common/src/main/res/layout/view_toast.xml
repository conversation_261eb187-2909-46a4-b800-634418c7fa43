<?xml version="1.0" encoding="utf-8"?>
<FrameLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.cardview.widget.CardView
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_margin="10dp"
        app:cardBackgroundColor="@color/white"
        app:cardCornerRadius="23dp"
        app:cardElevation="8dp">

        <ImageView
            android:id="@+id/iv_level"
            android:layout_width="20dp"
            android:layout_height="20dp"
            android:layout_gravity="center_vertical"
            android:layout_marginStart="18dp"
            android:src="@drawable/toast_hint"
            tools:ignore="ContentDescription" />

        <TextView
            android:id="@+id/tv_message"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_marginStart="44dp"
            android:layout_marginTop="12dp"
            android:layout_marginEnd="18dp"
            android:layout_marginBottom="12dp"
            android:maxWidth="400dp"
            android:textColor="@color/black_opaque_85"
            android:textSize="15sp" />
    </androidx.cardview.widget.CardView>
</FrameLayout>