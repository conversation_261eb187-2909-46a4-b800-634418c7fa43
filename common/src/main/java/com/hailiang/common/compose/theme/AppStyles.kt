package com.hailiang.common.compose.theme

import androidx.compose.foundation.Indication
import androidx.compose.foundation.clickable
import androidx.compose.foundation.interaction.MutableInteractionSource
import androidx.compose.runtime.getValue
import androidx.compose.runtime.mutableStateOf
import androidx.compose.runtime.remember
import androidx.compose.runtime.setValue
import androidx.compose.ui.Modifier
import androidx.compose.ui.composed

/**
 * 防止快速点击, clickInterval ms内点击无效
 * <AUTHOR>
 * @version 2025/4/22 15:23
 */
fun Modifier.antiMultiClick(
    clickInterval: Long = 500L,
    indication: Indication? = null,
    enabled: Boolean = true,
    onClick: () -> Unit,
): Modifier = composed {
    var lastClickTime by remember { mutableStateOf(0L) }
    then(
        clickable(
            interactionSource = remember { MutableInteractionSource() },
            indication = indication,
            enabled = enabled,
        ) {
            val currentTime = System.currentTimeMillis()
            if (currentTime - lastClickTime > clickInterval) {
                lastClickTime = currentTime
                onClick()
            }
        }
    )
}