package com.hailiang.common.compose.theme

import androidx.compose.foundation.layout.padding
import androidx.compose.foundation.layout.size
import androidx.compose.material3.Icon
import androidx.compose.runtime.Composable
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.painterResource
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.Dp
import androidx.compose.ui.unit.dp
import com.hailiang.xxb.common.R

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/22 09:29
 */

@Composable
fun BackIcon(onBackPressed: () -> Unit) {
    BackIcon(20.dp, 20.dp, onBackPressed)
}

@Composable
fun BackIcon(width: Dp, height: Dp, onBackPressed: () -> Unit) {
    Icon(
        modifier = Modifier
            .size(width = width, height = height)
            .padding(
                horizontal = (width - 16.dp).div(2),
                vertical = (height - 12.dp).div(2)
            )
            .antiMultiClick {
                onBackPressed.invoke()
            },
        painter = painterResource(R.drawable.back_arrow),
        contentDescription = null
    )
}

@Preview
@Composable
private fun BackIconPreview() {
    BackIcon(onBackPressed = {})
}