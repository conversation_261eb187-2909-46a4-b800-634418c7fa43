package com.hailiang.common.event.receiver

import android.content.Context
import android.content.Intent
import com.hailiang.common.event.FlowEventBus

class TimeChangeReceiver : BaseBroadcastReceiver() {

    override fun getActions(): List<String> {
        return listOf(
            Intent.ACTION_TIME_TICK,
            Intent.ACTION_TIME_CHANGED,
            Intent.ACTION_TIMEZONE_CHANGED
        )
    }

    override fun onReceive(context: Context, intent: Intent) {
        FlowEventBus.postEvent(TimeTickEvent())
    }
}