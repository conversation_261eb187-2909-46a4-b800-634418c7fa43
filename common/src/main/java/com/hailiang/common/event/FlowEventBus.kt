package com.hailiang.common.event

import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LifecycleOwner
import androidx.lifecycle.lifecycleScope
import androidx.lifecycle.repeatOnLifecycle
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.launchCatch
import kotlinx.coroutines.MainScope
import kotlinx.coroutines.flow.MutableSharedFlow
import kotlinx.coroutines.flow.collectLatest
import kotlinx.coroutines.launch

object FlowEventBus {
    private val events = mutableMapOf<String, MutableSharedFlow<Any>>()

    // 发送事件
    inline fun <reified T : Any> postEvent(event: T, key: String = T::class.java.name) {
        postEvent(key, event)
    }

    // 发送事件
    fun <T : Any> postEvent(key: String, event: T) {
        val flow = events.getOrPut(key) { MutableSharedFlow(replay = 0) }
        MainScope().launch {
            flow.emit(event)
        }
    }

    // 订阅事件（自动绑定生命周期）
    inline fun <reified T : Any> observeEventT(
        owner: LifecycleOwner,
        key: String = T::class.java.name,
        state: Lifecycle.State = Lifecycle.State.CREATED,
        noinline onEvent: (T) -> Unit
    ) {
        observeEvent(owner, key, state, onEvent)
    }

    fun <T : Any> observeEvent(
        owner: LifecycleOwner,
        key: String,
        state: Lifecycle.State = Lifecycle.State.CREATED,
        onEvent: (T) -> Unit
    ) {
        owner.lifecycleScope.launchCatch(errorBlock = { HLog.e("FlowEventBus", this.message, this) }) {
            owner.repeatOnLifecycle(state) {
                val flow = events.getOrPut(key) { MutableSharedFlow(replay = 0) }
                flow.collectLatest { event ->
                    onEvent(event as T)
                }
            }
        }
    }
}