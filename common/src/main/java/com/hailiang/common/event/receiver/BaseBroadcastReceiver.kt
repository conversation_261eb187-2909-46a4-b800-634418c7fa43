package com.hailiang.common.event.receiver

import android.content.BroadcastReceiver
import android.content.Context
import android.content.IntentFilter


abstract class BaseBroadcastReceiver : BroadcastReceiver() {
    fun register(context: Context) {
        val filter = IntentFilter()
        getActions().forEach {
            filter.addAction(it)
        }
        context.registerReceiver(this, filter)
    }

    abstract fun getActions(): List<String>

    fun unregister(context: Context) {
        context.unregisterReceiver(this)
    }
}