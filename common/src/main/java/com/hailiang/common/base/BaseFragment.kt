package com.hailiang.common.base

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import com.hailiang.core.base.BaseFragment

abstract class BaseFragment(private val layoutId: Int) : BaseFragment() {
    open var TAG: String = javaClass.simpleName
    open var enterTime: Long = System.currentTimeMillis()

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        enterTime = System.currentTimeMillis()
        val view = inflater.inflate(layoutId, container, false)
        view.isClickable = true
        return view
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        initView(view)
        observeData()
        //initData()父类会调用
    }

    open fun initView(view: View) {}

    open fun observeData() {}
}