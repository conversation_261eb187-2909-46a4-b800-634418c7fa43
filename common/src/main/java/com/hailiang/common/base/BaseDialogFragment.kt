package com.hailiang.common.base

import android.content.DialogInterface
import android.content.DialogInterface.OnDismissListener
import android.os.Bundle
import android.view.View
import android.view.WindowManager
import androidx.fragment.app.DialogFragment
import androidx.fragment.app.FragmentManager
import androidx.fragment.app.FragmentTransaction
import com.hailiang.hlutil.HLog

open class BaseDialogFragment : DialogFragment() {
    open val TAG: String = javaClass.simpleName
    open var onDismissListener: OnDismissListener? = null

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        view.isClickable = true
    }

    fun setBackgroundTransparent() {
        dialog?.window?.setBackgroundDrawableResource(android.R.color.transparent)
    }

    fun hideNavigationBar() {
        val uiOptions =
            View.SYSTEM_UI_FLAG_HIDE_NAVIGATION or View.SYSTEM_UI_FLAG_FULLSCREEN or View.SYSTEM_UI_FLAG_IMMERSIVE
        dialog?.window?.decorView?.setSystemUiVisibility(uiOptions)
    }

    fun setFullscreen() {
        dialog?.window?.apply {
            setBackgroundTransparent()

            //设置全屏，用style设置底部导航栏背景会变黑
            decorView.setPadding(0, 0, 0, 0)
            val lp = attributes
            lp.width = WindowManager.LayoutParams.MATCH_PARENT
            lp.height = WindowManager.LayoutParams.MATCH_PARENT
            attributes = lp

            hideNavigationBar()
        }
    }

    override fun show(manager: FragmentManager, ftag: String?) {
        try {
            super.show(manager, ftag)
        } catch (e: Exception) {
            HLog.e(TAG, "show dialog with name '" + javaClass.simpleName + "' failed, cause:" + e.message)
        }
    }

    override fun show(transaction: FragmentTransaction, ftag: String?): Int {
        var ret = -1
        try {
            ret = super.show(transaction, ftag)
        } catch (e: Exception) {
            HLog.e(TAG, "show dialog with name '" + javaClass.simpleName + "' failed, cause:" + e.message)
        }
        return ret
    }


    override fun dismiss() {
        try {
            super.dismiss()
        } catch (e: Exception) {
            dismissAllowingStateLoss()
            HLog.e(TAG, "dismiss dialog with name '" + javaClass.simpleName + "' failed, cause:" + e.message)
        }
    }

    override fun onDismiss(dialog: DialogInterface) {
        super.onDismiss(dialog)
        onDismissListener?.onDismiss(dialog)
    }
}