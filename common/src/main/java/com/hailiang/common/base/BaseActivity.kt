package com.hailiang.common.base

import android.content.pm.ActivityInfo
import android.os.Bundle
import android.view.Window
import com.hailiang.core.base.BaseActivity

open class BaseActivity : BaseActivity() {
    companion object {
        var orientation = ActivityInfo.SCREEN_ORIENTATION_LANDSCAPE
    }

    val TAG: String = javaClass.simpleName

    override fun onCreate(savedInstanceState: Bundle?) {
        supportRequestWindowFeature(Window.FEATURE_NO_TITLE)
        requestedOrientation = orientation
        super.onCreate(savedInstanceState)

        initView()
        observeData()
        initData()
    }

    override fun getFullScreenBehavior(): Int {
        return -1
    }

    override fun isFullScreen(): <PERSON><PERSON><PERSON> {
        return true
    }

    open fun initView() {}

    open fun observeData() {}

    open fun initData() {}
}