package com.hailiang.common.ext.recycleriew

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.GridLayoutManager
import androidx.recyclerview.widget.RecyclerView

class GridSpacingItemDecoration(
    private val horizontalSpacing: Int,
    private val verticalSpacing: Int
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val position = parent.getChildAdapterPosition(view)
        val spanCount = (parent.layoutManager as GridLayoutManager).spanCount

        // 横向间隔控制
        if (position % spanCount == 0) {
            outRect.left = 0
        } else {
            outRect.left = horizontalSpacing / 2
        }
        if (position % spanCount == spanCount - 1) {
            outRect.right = 0
        } else {
            outRect.right = horizontalSpacing / 2
        }

        // 纵向间隔控制
        val isFirstLine = position < spanCount
        if (!isFirstLine) {
            outRect.top = verticalSpacing
        }
    }
}