package com.hailiang.common.ext.recycleriew

import android.graphics.Rect
import android.view.View
import androidx.recyclerview.widget.LinearLayoutManager
import androidx.recyclerview.widget.RecyclerView

class LinearSpacingItemDecoration(
    private val spacing: Int,
    private val includeEdge: Boolean = false // 是否包含首尾边缘间隔
) : RecyclerView.ItemDecoration() {

    override fun getItemOffsets(
        outRect: Rect,
        view: View,
        parent: RecyclerView,
        state: RecyclerView.State
    ) {
        val orientation = (parent.layoutManager as LinearLayoutManager).orientation
        val position = parent.getChildAdapterPosition(view)
        val totalCount = parent.adapter?.itemCount ?: 0

        when (orientation) {
            RecyclerView.VERTICAL -> {
                // 垂直布局：底部添加间隔
                outRect.bottom = if (position < totalCount - 1) spacing else 0
                if (includeEdge) outRect.top = if (position == 0) spacing else 0
            }

            RecyclerView.HORIZONTAL -> {
                // 水平布局：右侧添加间隔
                outRect.right = if (position < totalCount - 1) spacing else 0
                if (includeEdge) outRect.left = if (position == 0) spacing else 0
            }
        }
    }
}
