package com.hailiang.common.interceptor;

import androidx.annotation.NonNull;

import com.hailiang.hlutil.HLog;

import java.io.IOException;

import okhttp3.Headers;
import okhttp3.Interceptor;
import okhttp3.MediaType;
import okhttp3.Request;
import okhttp3.Response;
import okio.Buffer;

public class CurlLoggerInterceptor implements Interceptor {
    @NonNull
    @Override
    public Response intercept(Chain chain) throws IOException {
        Request request = chain.request();

        StringBuilder curlCommand = new StringBuilder("curl -X ");
        curlCommand.append(request.method());

        Headers headers = request.headers();
        for (int i = 0; i < headers.size(); i++) {
            if ("Content-Type".equalsIgnoreCase(headers.name(i))) {
                continue;
            }
            curlCommand.append(" -H \"").append(headers.name(i)).append(": ").append(headers.value(i)).append("\"");
        }

        if (request.body() != null) {
            MediaType contentType = request.body().contentType();
            if (contentType != null) {
                curlCommand.append(" -H \"Content-Type: ").append(contentType).append("\"");
            }
            Buffer buffer = new Buffer();
            request.body().writeTo(buffer);
            String body = buffer.readUtf8();
            body = body.replace("'", "\\'");
            curlCommand.append(" -d '").append(body).append("'");
        }
        curlCommand.append(" ").append(request.url());

        HLog.d("CURL", curlCommand.toString());
        return chain.proceed(request);
    }
}