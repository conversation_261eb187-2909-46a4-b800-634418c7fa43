package com.hailiang.common.util;

import android.app.Activity;
import android.app.Application;
import android.content.BroadcastReceiver;
import android.content.ComponentName;
import android.content.Context;
import android.content.Intent;
import android.content.IntentFilter;
import android.os.Bundle;
import android.util.Log;


import com.hailiang.common.log.SlsLogHelper;

import java.util.List;
import java.util.Stack;


//Activity管理器
public class ActivityManager implements Application.ActivityLifecycleCallbacks {
    public static Stack<Activity> getActivityStack() {
        return activityStack;
    }

    public void setActivityStack(Stack<Activity> activityStack) {
        this.activityStack = activityStack;
    }

    private static Stack<Activity> activityStack = new Stack<Activity>();
    ;//第一个activity的onActivityCreated方法调用以后 就可以保证不为null
    private static Application application;
    private static Activity currentActivity;//第一个activity的onResume方法调用以后 就可以保证不为null

    /**
     * 记录用户进入时间
     * 全日志埋点记录用户停留时长用到
     */
    private static long userEnterTime;
    private boolean isInBackground = false;
    private long lastReportTime;

    public ActivityManager(Application application) {
        this.application = application;
        activityStack = new Stack<Activity>();
        userEnterTime = System.currentTimeMillis();

        //关机上报停留时长
        IntentFilter shutdownFilter = new IntentFilter();
        shutdownFilter.addAction(Intent.ACTION_SHUTDOWN);
        shutdownFilter.addAction(Intent.ACTION_REBOOT);
        application.registerReceiver(new BroadcastReceiver() {
            @Override
            public void onReceive(Context context, Intent intent) {
                SlsLogHelper.recordStayTime(userEnterTime, System.currentTimeMillis());
            }
        }, shutdownFilter);
    }


    public void addActivity(Activity activity) {
        if (activity == null) return; // 添加空指针保护
        if (activityStack == null) {
            activityStack = new Stack<Activity>();
        }
        // 避免重复添加同一个Activity
        if (!activityStack.contains(activity)) {
            activityStack.add(activity);
        }
        currentActivity = activity;
    }

    public static Activity getTopActivity() {
        if (activityStack == null || activityStack.isEmpty()) {
            return null;
        }
        return activityStack.lastElement();
    }


    public Activity getCurrentActivity() {
        if (activityStack == null || activityStack.isEmpty()) {
            return null;
        }
        return currentActivity;
    }

    public static void startActivity(Class<?> activityClass) {
        Intent intent = new Intent();
        if (currentActivity != null) {
            intent.setClass(currentActivity, activityClass);
            currentActivity.startActivity(intent);
        } else if (getTopActivity() != null) {
            intent.setClass(getTopActivity(), activityClass);
            getTopActivity().startActivity(intent);
        } else {
            intent.setFlags(Intent.FLAG_ACTIVITY_NEW_TASK);
            intent.setClass(application, activityClass);
            application.startActivity(intent);
        }
    }

    /**
     * 结束当前Activity（栈顶Activity）
     */
    public void finishActivity() {
        if (activityStack == null || activityStack.isEmpty()) {
            return;
        }
        Activity activity = activityStack.lastElement();
        finishActivity(activity);
    }

    /**
     * 结束指定的Activity(重载)
     */
    public static void finishActivity(Activity activity) {
        if (activity != null && activityStack != null) {
            activity.finish();
            activityStack.remove(activity);
            if (!activityStack.isEmpty()) {
                currentActivity = activityStack.lastElement();
            } else {
                currentActivity = null; // 修复：Stack为空时清空currentActivity
            }
        }
    }

    /**
     * 结束指定的Activity(重载)
     */
    public static void finishActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if (activity.getClass().equals(cls)) {
                finishActivity(activity);
                break;
            }
        }
    }

    /**
     * 关闭除了指定activity以外的全部activity 如果cls不存在于栈中，则栈全部清空
     *
     * @param cls
     */
    public void finishOthersActivity(Class<?> cls) {
        for (Activity activity : activityStack) {
            if (!activity.getClass().equals(cls)) {
                Log.e("jc", "finishOthersActivity" + activity.getClass());
                activity.finish();
            } else {
                currentActivity = activity;
            }
        }
        activityStack.clear();
        activityStack.add(currentActivity);
    }

    /**
     * 结束所有Activity
     */
    public static void finishAllActivity() {
        for (Activity activity : activityStack) {
            if (activity != currentActivity) {
                activity.finish();
            }
        }
        if(currentActivity != null) {
            currentActivity.finish();
            currentActivity = null;
        }
        activityStack.clear();
        //异常退出上报停留时长
        SlsLogHelper.recordStayTime(userEnterTime, System.currentTimeMillis());
    }

    /**
     * 应用程序退出
     */
    public static void appExit() {
        finishAllActivity();
//        application.onTerminate();
//        System.exit(0);
    }


    @Override
    public void onActivityCreated(Activity activity, Bundle savedInstanceState) {
        //屏幕适配
        addActivity(activity);
    }

    @Override
    public void onActivityStarted(Activity activity) {

    }

    @Override
    public void onActivityResumed(Activity activity) {
        currentActivity = activity;
        if (isInBackground) {
            userEnterTime = System.currentTimeMillis();
            isInBackground = false;
        }
    }

    @Override
    public void onActivityPaused(Activity activity) {

    }

    @Override
    public void onActivityStopped(Activity activity) {
        if (activity == currentActivity) {
            //不是跳转页面操作,退出应用或到后台
            isInBackground = true;
            //会连续调用两次??? 屏蔽一下重复上报
            if (System.currentTimeMillis() - lastReportTime < 1000) {
                return;
            }
            lastReportTime = System.currentTimeMillis();
            SlsLogHelper.recordStayTime(userEnterTime, System.currentTimeMillis());
        }
    }

    @Override
    public void onActivitySaveInstanceState(Activity activity, Bundle outState) {

    }

    @Override
    public void onActivityDestroyed(Activity activity) {
        // 修复内存泄漏：Activity销毁时必须从Stack中移除
        if (activityStack != null && activityStack.contains(activity)) {
            activityStack.remove(activity);
            // 如果移除的是当前Activity，更新currentActivity
            if (activity == currentActivity) {
                currentActivity = activityStack.isEmpty() ? null : activityStack.lastElement();
            }
        }
    }


    public boolean isActivityRunning(String activityClassName) {
        android.app.ActivityManager activityManager = (android.app.ActivityManager) application.getSystemService(Context.ACTIVITY_SERVICE);
        List<android.app.ActivityManager.RunningTaskInfo> info = activityManager.getRunningTasks(1);
        if (info != null && info.size() > 0) {
            ComponentName component = info.get(0).topActivity;
//            HLog.e("topActivity", component.getClassName());
            if (activityClassName.equals(component.getClassName())) {
                return true;
            }
        }
        return false;
    }


    /**
     * 结束所有Activity
     */
    public static void finishAllWithout(Activity withoutActivity) {
        for (Activity activity : activityStack) {
            if (activity != withoutActivity) {
                activity.finish();
            }
        }
    }

    public static void finishAllByClass(Activity targetActivity) {
        if(targetActivity == null) return;
        for (Activity activity : activityStack) {
            if (activity.getClass() == targetActivity.getClass()) {
                activity.finish();
            }
        }
    }
}