package com.hailiang.common.util

import android.graphics.Bitmap
import android.graphics.Canvas
import android.os.SystemClock
import android.view.View
import androidx.core.graphics.createBitmap
import com.hailiang.hlutil.FileUtil
import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import java.io.ByteArrayOutputStream
import java.io.File
import java.io.FileOutputStream
import java.io.IOException
import java.io.OutputStream
import java.util.UUID

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/4/29 21:36
 */
object BitmapUtil {

    fun writeToFile(
        bitmap: Bitmap?,
        outFile: File,
        format: Bitmap.CompressFormat = Bitmap.CompressFormat.PNG,
        quality: Int = 100,
    ) {
        bitmap ?: return
        var outputStream: OutputStream? = null
        val tempFile = File("${outFile.absolutePath.substringBeforeLast(".")}_${UUID.randomUUID()}.tmp")
        try {
            FileUtil.reCreateFile(tempFile)
            outputStream = FileOutputStream(tempFile)
            compressAndSave(bitmap, format, outputStream, quality)
            FileUtil.deleteFile(outFile)
            tempFile.renameTo(outFile)
        } catch (e: Exception) {
            HLog.w(HTag.TAG_ERROR, "bitmap write to file error ", e)
        } finally {
            FileUtil.deleteFile(tempFile)
            try {
                outputStream?.flush()
                outputStream?.close()
            } catch (e: IOException) {
                e.printStackTrace()
            }
        }
    }

    fun compressAndSave(
        bitmap: Bitmap,
        format: Bitmap.CompressFormat,
        stream: OutputStream,
        quality: Int,
    ) {
//        HLog.i(
//            HTag.TAG,
//            "compressAndSave width:${bitmap.width}; height: ${bitmap.height}; byteCount: ${bitmap.byteCount}; quality: $quality"
//        )
        val tempStream = ByteArrayOutputStream()
        try {
            bitmap.compress(format, quality, tempStream)
            stream.write(tempStream.toByteArray())
        } catch (e: Throwable) {
            HLog.w(HTag.TAG_ERROR, "compressAndSave error ", e)
        } finally {
            stream.flush()
            stream.close()
        }
    }

    fun getViewBitmap(
        view: View?,
        width: Int = view?.width ?: -1,
        height: Int = view?.height ?: -1,
    ): Bitmap? {
        if (view == null || width <= 0 || height <= 0) return null
        val bitmap = runCatching {
            createBitmap(width, height).also {
                val canvas = Canvas(it)
                canvas.translate((-view.scrollX).toFloat(), (-view.scrollY).toFloat())
                view.draw(canvas)
            }
        }.getOrNull()
        return bitmap
    }

    /**
     * 按指定宽度合并多个Bitmap
     * @param bitmaps 需要合并的Bitmap列表
     * @param targetWidth 目标宽度
     * @return 合并后的Bitmap
     */
    fun mergeBitmaps(bitmaps: List<Bitmap>, targetWidth: Int): Bitmap? {
        if (bitmaps.isEmpty()) return null
        // 计算所有Bitmap的总高度
        var totalHeight = 0
        for (bitmap in bitmaps) {
            // 按比例缩放高度
            val scale = targetWidth.toFloat() / bitmap.width
            totalHeight += (bitmap.height * scale).toInt()
        }
        HLog.i(
            HTag.TAG,
            "合并图片 bitmaps.size: ${bitmaps.size}; targetWidth: $targetWidth; totalHeight: $totalHeight"
        )
        // 创建目标Bitmap
        val mergedBitmap = Bitmap.createBitmap(targetWidth, totalHeight, Bitmap.Config.ARGB_8888)
        val canvas = Canvas(mergedBitmap)
        //
        var currentHeight = 0
        for (bitmap in bitmaps) {
            // 按比例缩放Bitmap
            val scale = targetWidth.toFloat() / bitmap.width
            val scaledHeight = (bitmap.height * scale).toInt()
            // 创建缩放后的Bitmap
            val scaledBitmap = Bitmap.createScaledBitmap(bitmap, targetWidth, scaledHeight, true)
            // 将Bitmap绘制到目标Bitmap上
            canvas.drawBitmap(scaledBitmap, 0f, currentHeight.toFloat(), null)
            currentHeight += scaledHeight
        }
        HLog.i(
            HTag.TAG,
            "合并图片 完成: mergedBitmap: ${mergedBitmap.width}x${mergedBitmap.height}"
        )
        return mergedBitmap
    }
}