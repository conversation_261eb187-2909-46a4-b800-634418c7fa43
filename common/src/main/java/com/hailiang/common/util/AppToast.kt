package com.hailiang.common.util

import android.annotation.SuppressLint
import android.content.res.ColorStateList
import android.os.Handler
import android.os.Looper
import android.view.Gravity
import android.view.LayoutInflater
import android.widget.ImageView
import android.widget.TextView
import android.widget.Toast
import androidx.core.graphics.toColorInt
import com.hailiang.hlutil.dpInt
import com.hailiang.hlutil.getApplication
import com.hailiang.xxb.common.R
import java.lang.ref.WeakReference

object AppToast {
    const val LEVEL_NORMAL = 0
    const val LEVEL_SUCCESS = 1
    const val LEVEL_FAIL = 2

    private var lastToast: WeakReference<Toast>? = null

    @SuppressLint("InflateParams")
    fun toast(msg: String, level: Int = LEVEL_NORMAL) {
        if (Thread.currentThread() != Looper.getMainLooper().thread) {
            Handler(Looper.getMainLooper()).post {
                toast(msg, level)
            }
            return
        }
        val toastView = LayoutInflater.from(getApplication()).inflate(R.layout.view_toast, null)
        val textView = toastView.findViewById<TextView>(R.id.tv_message)
        textView.text = msg

        val ivLevel = toastView.findViewById<ImageView>(R.id.iv_level)
        when (level) {
            LEVEL_NORMAL -> {
                ivLevel.setImageResource(R.drawable.toast_hint)
            }

            LEVEL_SUCCESS -> {
                ivLevel.setImageResource(R.drawable.toast_success)
                ivLevel.imageTintList = ColorStateList.valueOf("#229453".toColorInt())
            }

            LEVEL_FAIL -> {
                ivLevel.setImageResource(R.drawable.toast_error)
                ivLevel.imageTintList = ColorStateList.valueOf("#F34718".toColorInt())
            }
        }

        lastToast?.get()?.cancel()
        val toast = Toast(getApplication())
        toast.duration = Toast.LENGTH_SHORT
        toast.view = toastView
        toast.setGravity(Gravity.TOP or Gravity.CENTER_HORIZONTAL, 0, 67.dpInt)
        toast.show()
        lastToast = WeakReference(toast)
    }
}