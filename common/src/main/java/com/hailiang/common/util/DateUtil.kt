package com.hailiang.common.util

import com.hailiang.hlutil.date.DateUtil.DEFAULT_PATTERN
import java.util.TimeZone

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/5/23 17:46
 */
object DateUtil {

    fun stringToTimeMillis(timeString: String?, defaultTime: Long): Long {
        return com.hailiang.hlutil.date.DateUtil.stringToDate(
            timeString,
            "yyyy-MM-dd HH:mm:ss",
            TimeZone.getDefault()
        )?.time ?: defaultTime
    }
}