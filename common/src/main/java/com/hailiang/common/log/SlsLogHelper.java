package com.hailiang.common.log;

import android.os.Build;
import android.text.TextUtils;

import androidx.annotation.NonNull;

import com.blankj.utilcode.util.AppUtils;
import com.blankj.utilcode.util.DeviceUtils;
import com.blankj.utilcode.util.NetworkUtils;
import com.blankj.utilcode.util.ScreenUtils;
import com.blankj.utilcode.util.TimeUtils;
import com.google.gson.Gson;
import com.hailiang.common.base.XxbApplication;
import com.hailiang.common.util.Constants;
import com.hailiang.core.thread.ThreadPlugins;
import com.hailiang.hlsls.log.AppLogManager;
import com.hailiang.hlsls.model.BasePoint;
import com.hailiang.hlsls.sls.AliyunLogHelper;
import com.hailiang.hlutil.HLog;
import com.hailiang.hlutil.HTag;
import com.hailiang.opensdk.consts.UserType;
import com.hailiang.opensdk.user.UserInfo;

import org.json.JSONException;
import org.json.JSONObject;

import java.util.List;

public class SlsLogHelper {

    static {
        HLog.registerLogCaller(SlsLogHelper.class.getName());
    }

    public static void recordStayTime(long enterTime, long exitTime) {
//        record(SlsEventCode.COMMON,
//                "统计页面时长",
//                getTimeEventInfo(enterTime, exitTime),
//                fillBaseBusinessInfo(new JSONObject()),
//                getBasePoint(1, "app_launch", getTerminalInfo())
//        );
    }


    // ----------------------------------------------------------------------
    // ----------------------------------------------------------------------
    public static void recordLog(String eventCode, JSONObject eventJson, String eventTag) {
        record(eventCode, eventTag, eventJson, new JSONObject(), getBasePoint(3, "app", getTerminalInfo()));
    }

    /**
     * 埋点统计
     *
     * @param eventCode
     * @param eventJson
     * @param eventTag
     */
    public static void customBury(String eventCode, String eventTag, JSONObject eventJson, @NonNull JSONObject businessJson) {
        record(eventCode, eventTag, eventJson, businessJson, getBasePoint(1, "app", getTerminalInfo()));
    }

    private static void record(String eventCode, String eventTag, @NonNull JSONObject eventJson, @NonNull JSONObject businessJson, @NonNull BasePoint<String, String> basePoint) {
        try {
            AppLogManager.flowLog(eventTag + "(" + eventCode + "): " + eventJson);
            if (!TextUtils.isEmpty(eventTag)) {
                eventJson.put("tag", eventTag);
            }
            ThreadPlugins.INSTANCE.getMultiExecutorStub().execute(new Runnable() {
                @Override
                public void run() {
                    basePoint.setEventCode(eventCode);
                    try {
                        basePoint.setEventInfo(eventJson.toString());
                        basePoint.setBusinessInfo(fillBaseBusinessInfo(businessJson).toString());
                        String temp = new Gson().toJson(basePoint);
                        JSONObject jsonObject = new JSONObject(temp);
                        AliyunLogHelper.getInstance().sendLog(jsonObject);
                    } catch (JSONException e) {
                        HLog.e(HTag.TAG_ERROR, "LogStayTime", e);
                    }
                    AppLogManager.dump();
                }
            });
        } catch (JSONException e) {
            HLog.e(HTag.TAG_ERROR, "recordLog error", e);
        }
    }

    private static BasePoint<String, String> getBasePoint(int type, String terminal, BasePoint.TerminalInfo terminalInfo) {
        return new BasePoint<>(
                ScreenUtils.getScreenWidth(),
                ScreenUtils.getScreenHeight(),
                TimeUtils.getNowMills(),
                DeviceUtils.getMacAddress(),
                BasePoint.LevelType.INFO,
                type,
                terminal,
                terminalInfo
        );
    }

    private static BasePoint.TerminalInfo getTerminalInfo() {
        return new BasePoint.TerminalInfo(
                "Android",
                DeviceUtils.getSDKVersionName(),
                DeviceUtils.getManufacturer(),
                DeviceUtils.getModel(),
                AppUtils.getAppVersionCode() + "",
                AppUtils.getAppVersionName(),
                NetworkUtils.getNetworkType().toString()
        );
    }

    public static JSONObject fillBaseBusinessInfo(JSONObject businessInfo) {
        try {
            businessInfo.put("appCode", XxbApplication.getInstance().getPackageName());
            businessInfo.put("role", UserType.STUDENT.getType());
            UserInfo userInfo = Constants.INSTANCE.getUserInfo();
            if (userInfo != null) {
                businessInfo.put("userId", userInfo.getUserId());
                businessInfo.put("schoolId", userInfo.getSchoolId());
            }
            businessInfo.put("deviceDisplay", Build.DISPLAY);
            businessInfo.put("deviceTimeMillis", System.currentTimeMillis());
            businessInfo.put("versionCode", AppUtils.getAppVersionCode() + "");
            businessInfo.put("versionName", AppUtils.getAppVersionName());
        } catch (JSONException e) {
            HLog.e(HTag.TAG_ERROR, "fillBaseBusinessInfo error", e);
        }
        return businessInfo;
    }

    public static JSONObject getTimeEventInfo(long enterTime, long exitTime) {
        JSONObject eventInfo = new JSONObject();
        try {
            eventInfo.put("loginTime", enterTime);
            eventInfo.put("outTime", exitTime);
            eventInfo.put("stayTime", exitTime - enterTime);
        } catch (JSONException e) {
            HLog.e(HTag.TAG_ERROR, "fillBaseEventInfo error", e);
        }
        return eventInfo;
    }
}
