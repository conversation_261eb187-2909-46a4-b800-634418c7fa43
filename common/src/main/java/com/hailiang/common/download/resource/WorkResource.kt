package com.hailiang.common.download.resource

import com.hailiang.hlutil.EncryptionUtil

data class WorkResource(
    val url: String,
    val fileName: String,
    val fileType: String,
    val isZip: Boolean = false,
    val fileMd5: String? = null,
    val onDownloadSuccess: ((workResource: WorkResource, savedPath: String?) -> Unit)? = null,
) {
    companion object {
        fun createImageResource(
            url: String,
            fileMd5: String? = null,
            onDownloadSuccess: ((workResource: WorkResource, savedPath: String?) -> Unit)? = null,
        ): WorkResource {
            return WorkResource(
                url = url, fileName = "${EncryptionUtil.getMD5(url)}.jpg", // 根据 url md5 来作为名字
                fileType = MaterialConst.FileType.PICTURE, fileMd5 = fileMd5, onDownloadSuccess = onDownloadSuccess
            )
        }

        fun createZipFileResource(
            url: String,
            fileMd5: String? = null,
            onDownloadSuccess: ((workResource: WorkResource, savedPath: String?) -> Unit)? = null,
        ): WorkResource {
            return WorkResource(
                url = url, fileName = "${EncryptionUtil.getMD5(url)}.zip", // 根据 url md5 来作为名字
                fileType = MaterialConst.FileType.ZIP, fileMd5 = fileMd5, onDownloadSuccess = onDownloadSuccess
            )
        }
    }

    fun getLocalPath(): String {
        return LocalResourceManager.getLocalFilePath(fileType = fileType, fileName = fileName)
    }

    fun resourceExists(): Boolean {
        return LocalResourceManager.resourceExists(this.getLocalPath(), this.fileMd5)
    }
}