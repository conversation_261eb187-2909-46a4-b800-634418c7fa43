package com.hailiang.common.download.resource

import com.hailiang.hlutil.HLog
import com.hailiang.hlutil.HTag
import com.hailiang.libhttp.download.DownloadCallback
import com.hailiang.libhttp.download.DownloadRequest
import com.hailiang.libhttp.download.HttpDownload
import com.hailiang.workcloud.download.resource.ResourceDownloadCallback

/**
 * 整理统一的资源下载
 * 后续准备都从这里下
 */
object WorkResourceDownload {

    /**
     * 批量顺序下载，不并发
     */
    fun downloadResources(
        resources: List<WorkResource>,
        downloadListener: ResourceDownloadCallback? = null,
    ) {
        downloadWithPosition(
            resources = resources,
            position = 0,
            downloadListener = downloadListener
        )
    }

    /**
     * 按位置下载，下载完成后再下载下一个
     */
    private fun downloadWithPosition(
        resources: List<WorkResource>,
        position: Int,
        downloadListener: ResourceDownloadCallback? = null,
    ) {
        resources.getOrNull(position)?.also { workResource ->
            downloadResource(workResource, object : DownloadCallback {
                override fun onFailure(errorMessage: String?, savePath: String) {
                    downloadListener?.onFail(workResource, errorMessage)
                    downloadWithPosition(resources, position + 1, downloadListener)
                }

                override fun onProgress(
                    fileTotalSize: Long,
                    fileDownSize: Long,
                    costTimeMillis: Long,
                ) {
                }

                override fun onStart(total: Long) {
                    downloadListener?.onStartDownload(workResource)
                }

                override fun onSuccess(message: String?, savePath: String, costTimeMillis: Long) {
                    downloadListener?.onFinishDownload(workResource, savePath)
                    workResource.onDownloadSuccess?.invoke(workResource, savePath)
                    downloadWithPosition(resources, position + 1, downloadListener)
                }
            })
        } ?: let { // 下载完成
            downloadListener?.onAllComplete()
        }
    }

    private fun downloadResource(
        resource: WorkResource,
        downloadListener: DownloadCallback? = null,
    ) {
        HLog.d(HTag.TAG, "downloadResource:  $resource")
        download(
            url = resource.url,
            fileName = resource.fileName,
            fileType = resource.fileType,
            fileMd5 = resource.fileMd5,
            downloadListener = downloadListener
        )
    }

    fun download(
        url: String,
        fileName: String,
        fileType: String,
        fileMd5: String? = null,
        downloadListener: DownloadCallback?,
    ) {
        val localFilePath = LocalResourceManager.getLocalFilePath(fileType, fileName)
        val request = DownloadRequest(url = url, savePath = localFilePath, md5 = fileMd5)
        HttpDownload.download(request, object : DownloadCallback {
            override fun onFailure(errorMessage: String?, savePath: String) {
                downloadListener?.onFailure(errorMessage = errorMessage, savePath = savePath)
            }

            override fun onProgress(fileTotalSize: Long, fileDownSize: Long, costTimeMillis: Long) {
                downloadListener?.onProgress(
                    fileTotalSize = fileTotalSize,
                    fileDownSize = fileDownSize,
                    costTimeMillis = costTimeMillis
                )
            }

            override fun onStart(total: Long) {
                downloadListener?.onStart(total)
            }

            override fun onSuccess(message: String?, savePath: String, costTimeMillis: Long) {
                downloadListener?.onSuccess(
                    message = message,
                    savePath = savePath,
                    costTimeMillis = costTimeMillis
                )
            }
        })
    }
}