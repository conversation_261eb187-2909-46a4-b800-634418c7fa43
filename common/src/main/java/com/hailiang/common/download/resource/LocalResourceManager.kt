package com.hailiang.common.download.resource

import android.os.Environment
import com.hailiang.common.base.XxbApplication
import com.hailiang.hlutil.EncryptionUtil
import com.hailiang.hlutil.FileUtil
import java.io.File

/**
 * Description:
 *
 * <AUTHOR>
 * @version 2025/1/22 19:40
 */
object LocalResourceManager {
    val documentPath: String
        get() = XxbApplication.getInstance()
            .getExternalFilesDir(Environment.DIRECTORY_DOCUMENTS)?.absolutePath
            ?: "/sdcard/Android/data/${XxbApplication.getInstance().packageName}/files/Documents"

    private val audioSuffix = setOf(".mp3", ".mp4")

    fun getSaveDirPath(fileType: String): String {
        return "${documentPath}/work/resource/$fileType"
    }

    fun getLocalFilePath(fileType: String, fileName: String): String {
        return "${getSaveDirPath(fileType = fileType)}/${fileName}"
    }

    fun findLocalVoiceFile(url: String?): String? {
        if (url.isNullOrEmpty()) return null
        val name = url.split("/").last()
        // 根据后缀匹配
        if (audioSuffix.any { name.endsWith(it, ignoreCase = true) }) {
            return getLocalFilePath(MaterialConst.FileType.AUDIO, name)
        }
        return null
    }

    fun localImageExists(fileName: String): Boolean {
        return resourceExists(getLocalFilePath(MaterialConst.FileType.PICTURE, fileName), null)
    }

    fun localVoiceExists(fileName: String): Boolean {
        return resourceExists(getLocalFilePath(MaterialConst.FileType.AUDIO, fileName), null)
    }

    fun resourceExists(localPath: String, md5: String?): Boolean {
        if (FileUtil.exists(localPath) && File(localPath).length() > 0) {
            if (md5.isNullOrEmpty()) {
                return true
            }
            return EncryptionUtil.getMD5(File(localPath)) == md5
        }
        return false
    }
    // ----------------------------------------------------------------------

    fun getLocalPath(url: String?): String? {
        var filePath = findLocalVoiceFile(url) ?: return null
        if (FileUtil.exists(filePath)) {
            return filePath
        }
        return null
    }

}